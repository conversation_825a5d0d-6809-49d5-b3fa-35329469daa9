import os
import subprocess
import sys
import requests
import webbrowser
import tkinter as tk
from tkinter import messagebox, ttk

# Configuración DPI para Windows
if sys.platform == "win32":
    try:
        import ctypes
        # For Windows 10 version 1607 and later: PROCESS_PER_MONITOR_DPI_AWARE
        ctypes.windll.shcore.SetProcessDpiAwareness(2)
    except (ImportError, AttributeError):
        try:
            # For Windows 8.1 and earlier:
            ctypes.windll.user32.SetProcessDPIAware()
        except (ImportError, AttributeError):
            pass

# Configuración para el manejo de imágenes
try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

# Importar openpyxl y pandas para ajuste de columnas
try:
    import pandas as pd
    import openpyxl
    from openpyxl.utils import get_column_letter
    from openpyxl.styles import Font, Border, Side
    EXCEL_PROCESSING_AVAILABLE = True
except ImportError:
    EXCEL_PROCESSING_AVAILABLE = False

def resource_path(relative_path):
    """
    Obtiene la ruta absoluta a los recursos, tanto para desarrollo como para ejecutables generados con PyInstaller.
    """
    try:
        # PyInstaller crea una carpeta temporal y guarda la ruta en _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")

    return os.path.join(base_path, relative_path)

def obtener_ruta_aplicacion():
    """
    Obtiene la ruta del directorio en el que se encuentra el ejecutable o el script.
    """
    if getattr(sys, 'frozen', False):
        # En modo ejecutable (PyInstaller)
        return os.path.dirname(sys.executable)
    else:
        # En modo desarrollo
        return os.path.dirname(os.path.abspath(__file__))

def crear_carpeta_reportes():
    """
    Crea la carpeta 'Reportes - Operaciones' en la misma ruta que el ejecutable, si no existe.
    """
    ruta_app = obtener_ruta_aplicacion()
    ruta_reportes = os.path.join(ruta_app, "Reportes - Operaciones")
    if not os.path.exists(ruta_reportes):
        try:
            os.makedirs(ruta_reportes)
        except Exception as e:
            messagebox.showerror("Error", f"No se pudo crear la carpeta 'Reportes - Operaciones':\n{e}")
    return ruta_reportes

def crear_carpeta_bdd():
    """
    Crea la carpeta 'BB.DD - Operaciones' en la misma ruta que el ejecutable, si no existe.
    """
    ruta_app = obtener_ruta_aplicacion()
    ruta_bdd = os.path.join(ruta_app, "BB.DD - Operaciones")
    if not os.path.exists(ruta_bdd):
        try:
            os.makedirs(ruta_bdd)
        except Exception as e:
            messagebox.showerror("Error", f"No se pudo crear la carpeta 'BB.DD - Operaciones':\n{e}")
    return ruta_bdd

def abrir_archivo(ruta_archivo):
    """Abre un archivo con la aplicación predeterminada del sistema."""
    try:
        if sys.platform == 'win32':
            os.startfile(ruta_archivo)
        else:
            # Para macOS y Linux usamos subprocess
            if sys.platform == 'darwin':  # macOS
                cmd = ['open', ruta_archivo]
            else:  # Linux
                cmd = ['xdg-open', ruta_archivo]
            subprocess.call(cmd)
        return True
    except Exception as e:
        messagebox.showerror("Error", f"Error al abrir el archivo: {e}")
        return False

def ajustar_columnas(ruta_archivo, status_callback=None):
    """Ajusta el ancho de las columnas del archivo Excel basado únicamente en los títulos y aplica formato."""
    if not EXCEL_PROCESSING_AVAILABLE:
        if status_callback:
            status_callback("Librerías de Excel no disponibles")
        return False
        
    try:
        if status_callback:
            status_callback("Ajustando anchos de columnas y aplicando formato...")
            
        # Leer el DataFrame para obtener dimensiones
        df = pd.read_excel(ruta_archivo)
        
        # Abrir el archivo Excel con openpyxl
        wb = openpyxl.load_workbook(ruta_archivo)
        hoja = wb.active
        
        # Definir estilos para las cabeceras
        font_negrita = Font(bold=True)
        border_delgado = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # Autoajustar ancho de columnas basado únicamente en los títulos y aplicar formato
        for col in range(1, len(df.columns) + 1):
            col_letra = get_column_letter(col)
            
            # Calcular el ancho basado únicamente en el título de la columna
            column_title = str(hoja.cell(row=1, column=col).value)
            header_length = len(column_title)
            
            # Establecer un ancho mínimo de 11 caracteres
            column_width = max(header_length + 2, 11)  # +2 para margen
            
            # Ajustar el ancho de la columna
            hoja.column_dimensions[col_letra].width = column_width
            
            # Aplicar formato a la cabecera (fila 1)
            celda_cabecera = hoja.cell(row=1, column=col)
            celda_cabecera.font = font_negrita
            celda_cabecera.border = border_delgado
        
        # Guardar el archivo con los ajustes
        wb.save(ruta_archivo)
        
        if status_callback:
            status_callback("Columnas ajustadas y formato aplicado correctamente")
        return True
    except Exception as e:
        if status_callback:
            status_callback("Error al ajustar anchos de columnas")
        print(f"Error al ajustar anchos de columnas: {e}")
        return False

def descargar_excel(url, nombre_archivo, status_callback=None):
    """
    Función para descargar un archivo Excel desde una URL y guardarlo en la carpeta 'Reportes - Operaciones'.
    """
    try:
        if status_callback:
            status_callback("Descargando archivo...")
            
        respuesta = requests.get(url, stream=True, timeout=30)
        respuesta.raise_for_status()  # Lanza una excepción si la solicitud falla

        ruta_app = obtener_ruta_aplicacion()
        # Se construye la ruta completa: [ruta_app]/Reportes - Operaciones/[nombre_archivo]
        ruta_reportes = os.path.join(ruta_app, "Reportes - Operaciones")
        if not os.path.exists(ruta_reportes):
            os.makedirs(ruta_reportes)
        nombre_archivo_completo = os.path.join(ruta_reportes, nombre_archivo)

        with open(nombre_archivo_completo, 'wb') as archivo:
            for chunk in respuesta.iter_content(chunk_size=8192):
                archivo.write(chunk)

        # Ajustar columnas del archivo Excel descargado
        if EXCEL_PROCESSING_AVAILABLE:
            ajustar_columnas(nombre_archivo_completo, status_callback)

        if status_callback:
            status_callback(f"Archivo descargado: {nombre_archivo}")
        messagebox.showinfo("Descarga completada", f"Archivo '{nombre_archivo}' descargado exitosamente en:\n{nombre_archivo_completo}")
        return True
    except requests.exceptions.RequestException as e:
        if status_callback:
            status_callback("Error al descargar el archivo")
        messagebox.showerror("Error de descarga", f"Error al descargar el archivo:\n{e}")
        return False

def abrir_excel(nombre_archivo, status_callback=None):
    """
    Función para abrir el archivo Excel descargado desde la carpeta 'Reportes - Operaciones'.
    """
    ruta_app = obtener_ruta_aplicacion()
    ruta_reportes = os.path.join(ruta_app, "Reportes - Operaciones")
    nombre_archivo_completo = os.path.join(ruta_reportes, nombre_archivo)

    if os.path.exists(nombre_archivo_completo):
        if abrir_archivo(nombre_archivo_completo):
            if status_callback:
                status_callback(f"Archivo abierto: {nombre_archivo}")
    else:
        if status_callback:
            status_callback("Archivo no encontrado")
        messagebox.showerror("Error", "El archivo no existe. Por favor, descárguelo primero.")

# Constantes para la gestión de bases de datos
BDD_TIPOS = {
    'Materiales BB.DD': 'materiales',
    'Equipos BB.DD': 'equipos', 
    'Vehículos BB.DD': 'vehiculos',
    'Personal BB.DD': 'personal'
}

BDD_FILENAMES = {
    'materiales': 'operaciones_materiales.csv',
    'equipos': 'operaciones_equipos.csv',
    'vehiculos': 'operaciones_vehiculos.csv',
    'personal': 'operaciones_personal.csv'
}

class SyaOperacionesApp:
    def __init__(self, root):
        self.root = root
        self.root.title("S&A - Area Operaciones")
        self.root.resizable(True, True)
        self.root.configure(bg='#f0f0f0')
        
        # Variables para almacenar rutas de archivos de bases de datos
        self.archivos_bdd = {}
        
        # Configuración del icono
        try:
            self.root.iconbitmap(resource_path("images/smontyaragon.ico"))
        except Exception:
            pass
        
        # Configurar estilos
        self.configurar_estilos()
        
        # Crear carpetas necesarias
        crear_carpeta_reportes()
        crear_carpeta_bdd()
        
        # Inicializar la interfaz
        self.inicializar_interfaz()
        
        # Ajustar el tamaño mínimo de la ventana
        root.update_idletasks()
        root.minsize(int(root.winfo_reqwidth()*1.50), root.winfo_reqheight())
    
    def configurar_estilos(self):
        """Configura los estilos de la interfaz."""
        style = ttk.Style(self.root)
        style.theme_use('clam')

        style.configure("Title.TLabel", font=("Helvetica", 16, "bold"), foreground="#333", background='#f0f0f0')

        style.configure("Base.TButton", font=("Helvetica", 14), padding=(15, 12), relief="flat", background="#e0e0e0", foreground="#333")
        style.map("Base.TButton",
                background=[("active", "#f0f0f0")],
                relief=[("active", "raised")]
                )

        style.configure("Descargar.TButton", parent="Base.TButton", background="#cce0ff", foreground="#003366")
        style.map("Descargar.TButton",
                background=[("active", "#b3d1ff")],
                foreground=[("active", "#003366")]
                )

        style.configure("Abrir.TButton", parent="Base.TButton", background="#90EE90", foreground="#006400") # Verde claro
        style.map("Abrir.TButton",
                background=[("active", "#7FFFD4")], # Acuamarine
                foreground=[("active", "#006400")]
                )

        style.configure("Subir.TButton", parent="Base.TButton", background="#FFB6C1", foreground="#A52A2A") # Rosa claro
        style.map("Subir.TButton",
                background=[("active", "#FFA07A")], # Salmón claro
                foreground=[("active", "#A52A2A")]
                )

        style.configure("General.TButton", parent="Base.TButton", background="#FFE4B5", foreground="#8B4513") # Beige
        style.map("General.TButton",
                background=[("active", "#FFDAB9")], # Peach Puff
                foreground=[("active", "#8B4513")]
                )
    
    def inicializar_interfaz(self):
        """Inicializa todos los componentes de la interfaz."""
        # Título
        titulo = ttk.Label(self.root, text="S&A - Area Operaciones", style="Title.TLabel")
        titulo.pack(pady=20)
        
        # Cargar logo
        self.cargar_logo()
        
        # Frame principal para botones
        frame_botones_principal = ttk.Frame(self.root)
        frame_botones_principal.pack(pady=20, fill='x', expand=True)
        
        # Sección Reportes Diarios
        frame_reportes = ttk.LabelFrame(frame_botones_principal, text="Reportes Diarios", padding=(10, 5))
        frame_reportes.pack(pady=10, padx=10, fill='x')

        btn_descargar_reporte = ttk.Button(
            frame_reportes, 
            text="Descargar Reporte Diario", 
            command=self.on_descargar, 
            style="Descargar.TButton"
        )
        btn_descargar_reporte.pack(side='left', padx=5, expand=True, fill='x')

        btn_abrir_reporte = ttk.Button(
            frame_reportes, 
            text="Abrir Reporte Diario", 
            command=self.on_abrir, 
            style="Abrir.TButton"
        )
        btn_abrir_reporte.pack(side='left', padx=5, expand=True, fill='x')
        
        # Sección Requerimientos
        frame_requerimientos = ttk.LabelFrame(frame_botones_principal, text="Requerimientos de Obra", padding=(10, 5))
        frame_requerimientos.pack(pady=10, padx=10, fill='x')

        btn_descargar_req = ttk.Button(
            frame_requerimientos, 
            text="Descargar Requerimientos", 
            command=self.on_descargar_requerimientos, 
            style="Descargar.TButton"
        )
        btn_descargar_req.pack(side='left', padx=5, pady=5, expand=True, fill='x')

        btn_abrir_req = ttk.Button(
            frame_requerimientos, 
            text="Abrir Requerimientos", 
            command=self.on_abrir_requerimientos, 
            style="Abrir.TButton"
        )
        btn_abrir_req.pack(side='left', padx=5, pady=5, expand=True, fill='x')
        
        # Sección Base de Datos Operaciones
        frame_bdd = ttk.LabelFrame(frame_botones_principal, text="Base de Datos Operaciones (CSV)", padding=(10, 5))
        frame_bdd.pack(pady=10, padx=10, fill='x')

        # Frame para selector de tipo de base de datos
        frame_selector = ttk.Frame(frame_bdd)
        frame_selector.pack(fill='x', pady=5)

        ttk.Label(frame_selector, text="Seleccionar BB.DD:").pack(side='left', padx=5)
        
        self.bdd_selector = ttk.Combobox(frame_selector, values=list(BDD_TIPOS.keys()), state="readonly", width=20)
        self.bdd_selector.pack(side='left', padx=5)
        self.bdd_selector.current(0)  # Seleccionar el primer elemento por defecto

        # Frame para botones de base de datos
        frame_botones_bdd = ttk.Frame(frame_bdd)
        frame_botones_bdd.pack(fill='x', pady=5)

        btn_descargar_bdd = ttk.Button(
            frame_botones_bdd, 
            text="Descargar BB.DD.", 
            command=self.descargar_bdd, 
            style="Descargar.TButton"
        )
        btn_descargar_bdd.pack(side='left', padx=5, expand=True, fill='x')

        btn_abrir_bdd = ttk.Button(
            frame_botones_bdd, 
            text="Abrir BB.DD.", 
            command=self.abrir_bdd, 
            style="Abrir.TButton"
        )
        btn_abrir_bdd.pack(side='left', padx=5, expand=True, fill='x')

        btn_subir_bdd = ttk.Button(
            frame_botones_bdd, 
            text="Subir BB.DD.", 
            command=self.subir_bdd, 
            style="Subir.TButton"
        )
        btn_subir_bdd.pack(side='left', padx=5, expand=True, fill='x')
        
        # Sección General
        frame_general = ttk.LabelFrame(frame_botones_principal, text="General", padding=(10, 5))
        frame_general.pack(pady=10, padx=10, fill='x')

        btn_abrir_carpeta = ttk.Button(
            frame_general, 
            text="Abrir Carpeta Reportes", 
            command=self.abrir_carpeta_reportes, 
            style="General.TButton"
        )
        btn_abrir_carpeta.pack(side='left', padx=5, expand=True, fill='x')

        btn_abrir_carpeta_bdd = ttk.Button(
            frame_general, 
            text="Abrir Carpeta BB.DD", 
            command=self.abrir_carpeta_bdd, 
            style="General.TButton"
        )
        btn_abrir_carpeta_bdd.pack(side='left', padx=5, expand=True, fill='x')
        
        # Etiqueta de estado
        self.status_label = ttk.Label(self.root, text="Listo", font=("Helvetica", 10), background="#f0f0f0")
        self.status_label.pack(side=tk.BOTTOM, pady=10)
    
    def cargar_logo(self):
        """Carga e inserta el logo de la empresa."""
        if PIL_AVAILABLE:
            try:
                image = Image.open(resource_path("images/smontyaragon.png"))
                photo = ImageTk.PhotoImage(image)
                label_imagen = ttk.Label(self.root, image=photo)
                label_imagen.image = photo
                label_imagen.pack(pady=15)
            except Exception:
                # Si no se puede cargar la imagen, mostrar un texto alternativo
                label_texto = ttk.Label(self.root, text="S&A Operaciones", font=("Helvetica", 20, "bold"), foreground="#0066cc")
                label_texto.pack(pady=15)
        else:
            # Si PIL no está disponible, mostrar un texto alternativo
            label_texto = ttk.Label(self.root, text="S&A Operaciones", font=("Helvetica", 20, "bold"), foreground="#0066cc")
            label_texto.pack(pady=15)
    
    def actualizar_estado(self, texto):
        """Actualiza el texto del label de estado."""
        self.status_label.config(text=texto)
        self.root.update()
    
    def on_descargar(self):
        """
        Evento del botón 'Descargar Reporte Diario'.
        """
        self.actualizar_estado("Descargando reporte diario...")
        url_servidor = "http://*************:5000/descargar-excel"  # URL del servidor
        # Se especifica solo el nombre del archivo para colocarlo dentro de la carpeta 'reportes'
        nombre_archivo_local = "Reporte Diario - Operaciones.xlsx"
        descargar_excel(url_servidor, nombre_archivo_local, self.actualizar_estado)

    def on_abrir(self):
        """
        Evento del botón 'Abrir Reporte Diario'.
        """
        nombre_archivo_local = "Reporte Diario - Operaciones.xlsx"
        abrir_excel(nombre_archivo_local, self.actualizar_estado)

    def on_descargar_requerimientos(self):
        """
        Evento del botón 'Descargar Requerimientos'.
        """
        self.actualizar_estado("Descargando requerimientos...")
        url_servidor = "http://*************:5000/descargar-requerimientos-excel"  # URL del servidor para requerimientos
        nombre_archivo_local = "Requerimientos - Operaciones.xlsx"
        descargar_excel(url_servidor, nombre_archivo_local, self.actualizar_estado)

    def on_abrir_requerimientos(self):
        """
        Evento del botón 'Abrir Requerimientos'.
        """
        nombre_archivo_local = "Requerimientos - Operaciones.xlsx"
        abrir_excel(nombre_archivo_local, self.actualizar_estado)
    
    def abrir_carpeta_reportes(self):
        """Abre la carpeta de reportes."""
        ruta_reportes = crear_carpeta_reportes()
        try:
            if abrir_archivo(ruta_reportes):
                self.actualizar_estado("Carpeta de reportes abierta")
        except Exception as e:
            self.actualizar_estado("Error al abrir la carpeta")
            messagebox.showerror("Error", f"Ocurrió un error al abrir la carpeta de reportes:\n{e}")

    def abrir_carpeta_bdd(self):
        """Abre la carpeta de bases de datos."""
        ruta_bdd = crear_carpeta_bdd()
        try:
            if abrir_archivo(ruta_bdd):
                self.actualizar_estado("Carpeta de BB.DD abierta")
        except Exception as e:
            self.actualizar_estado("Error al abrir la carpeta BB.DD")
            messagebox.showerror("Error", f"Ocurrió un error al abrir la carpeta de BB.DD:\n{e}")

    def get_tipo_bdd_seleccionado(self):
        """Obtiene el tipo de base de datos seleccionado en el combobox."""
        seleccion = self.bdd_selector.get()
        return BDD_TIPOS.get(seleccion, 'materiales')

    def descargar_bdd(self):
        """Descarga el archivo CSV de base de datos seleccionado desde el servidor."""
        tipo_bdd = self.get_tipo_bdd_seleccionado()
        nombre_archivo = BDD_FILENAMES[tipo_bdd]
        
        self.actualizar_estado(f"Descargando BB.DD. {tipo_bdd}...")
        
        # Preparar ruta de destino en la carpeta BB.DD - Operaciones
        ruta_bdd = crear_carpeta_bdd()
        ruta_archivo_bdd = os.path.join(ruta_bdd, nombre_archivo)
        
        # URL del servidor
        url = f"http://*************:5000/api/operaciones/descargar-bdd/{tipo_bdd}"
        
        try:
            # Descargar archivo
            respuesta = requests.get(url, stream=True, timeout=30)
            respuesta.raise_for_status()

            with open(ruta_archivo_bdd, 'wb') as archivo:
                for chunk in respuesta.iter_content(chunk_size=8192):
                    archivo.write(chunk)

            self.actualizar_estado(f"BB.DD. {tipo_bdd} descargada")
            messagebox.showinfo("Descarga Completada",
                               f"La base de datos de {tipo_bdd} ha sido descargada correctamente.")
            
            # Guardar la ruta del archivo descargado
            self.archivos_bdd[tipo_bdd] = ruta_archivo_bdd
            
        except requests.exceptions.RequestException as e:
            self.actualizar_estado("Error al descargar BB.DD.")
            messagebox.showerror("Error de Conexión", f"No se pudo conectar al servidor:\n{e}")
        except Exception as e:
            self.actualizar_estado("Error al descargar BB.DD.")
            messagebox.showerror("Error", f"Ocurrió un error al descargar el archivo:\n{e}")

    def abrir_bdd(self):
        """Abre el último archivo CSV de base de datos descargado del tipo seleccionado."""
        tipo_bdd = self.get_tipo_bdd_seleccionado()
        
        try:
            # Verificar si tenemos el archivo en memoria
            if tipo_bdd in self.archivos_bdd and os.path.exists(self.archivos_bdd[tipo_bdd]):
                if abrir_archivo(self.archivos_bdd[tipo_bdd]):
                    self.actualizar_estado(f"BB.DD. {tipo_bdd} abierta")
            else:
                # Buscar el archivo en la carpeta de BB.DD
                ruta_bdd = crear_carpeta_bdd()
                nombre_archivo = BDD_FILENAMES[tipo_bdd]
                archivo_potencial = os.path.join(ruta_bdd, nombre_archivo)

                if os.path.exists(archivo_potencial):
                    self.archivos_bdd[tipo_bdd] = archivo_potencial
                    if abrir_archivo(archivo_potencial):
                        self.actualizar_estado(f"BB.DD. {tipo_bdd} abierta")
                else:
                    messagebox.showinfo(
                        "Información",
                        f"No hay archivo de BB.DD. {tipo_bdd} para abrir. Por favor, descárguelo primero."
                    )
                    self.actualizar_estado(f"No hay BB.DD. {tipo_bdd} para abrir")
        except Exception as e:
            self.actualizar_estado(f"Error al abrir BB.DD. {tipo_bdd}")
            messagebox.showerror("Error", f"Ocurrió un error al abrir el archivo de BB.DD. {tipo_bdd}:\n{e}")

    def subir_bdd(self):
        """Sube el archivo CSV de base de datos al servidor."""
        tipo_bdd = self.get_tipo_bdd_seleccionado()
        nombre_archivo = BDD_FILENAMES[tipo_bdd]
        
        # Verificar que el archivo existe
        archivo_a_subir = None
        if tipo_bdd in self.archivos_bdd and os.path.exists(self.archivos_bdd[tipo_bdd]):
            archivo_a_subir = self.archivos_bdd[tipo_bdd]
        else:
            # Buscar el archivo en la carpeta de BB.DD
            ruta_bdd = crear_carpeta_bdd()
            archivo_potencial = os.path.join(ruta_bdd, nombre_archivo)
            if os.path.exists(archivo_potencial):
                archivo_a_subir = archivo_potencial
                self.archivos_bdd[tipo_bdd] = archivo_potencial

        if not archivo_a_subir:
            messagebox.showerror(
                "Error", 
                f"No se encuentra el archivo '{nombre_archivo}'.\nPor favor, descárguelo o asegúrese de que exista en la carpeta BB.DD - Operaciones."
            )
            self.actualizar_estado(f"BB.DD. {tipo_bdd} no encontrada para subir")
            return

        self.actualizar_estado(f"Subiendo BB.DD. {tipo_bdd}...")
        
        # URL del servidor
        url = f"http://*************:5000/api/operaciones/subir-bdd/{tipo_bdd}"
        
        try:
            with open(archivo_a_subir, 'rb') as f:
                files = {'file': (nombre_archivo, f)}
                response = requests.post(url, files=files, timeout=60)
                response.raise_for_status()
                
            self.actualizar_estado(f"BB.DD. {tipo_bdd} subida correctamente")
            messagebox.showinfo(
                "Carga Completada",
                f"La base de datos de {tipo_bdd} ha sido subida correctamente."
            )
        except requests.exceptions.RequestException as e:
            self.actualizar_estado("Error al subir BB.DD.")
            messagebox.showerror("Error de Conexión", f"No se pudo conectar al servidor:\n{e}")
        except Exception as e:
            self.actualizar_estado("Error al subir BB.DD.")
            messagebox.showerror("Error", f"Ocurrió un error al subir el archivo:\n{e}")


# Punto de entrada principal
def main():
    """Inicializa y ejecuta la aplicación principal."""
    root = tk.Tk()
    app = SyaOperacionesApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()