# Software para el Area de Operaciones - Smont y Aragon SAC

- Aplicación móvil (Android):           sya_operaciones_android.py
- Aplicación de escritorio (Windows):   sya_operaciones_desktop.py

## Avance 30 enero 2025:
- Implementando Equipos Usado<PERSON>, Vehículos Usados y Personal de Campo.
- Reunion Ing. Huillca

## Avance 29 enero 2025:
- Implementacion completa de Materiales Usados.

## Avance 28 enero 2025:
- Trabajando en el excel que genera el servidor para recibir correctamente los datos de los formularios.
- El archivo excel tiene las siguientes hojas: Reporte Principal, Materiales Usados, Equipos Usados, Vehículos Usados y Personal de Campo.
- Implementacion completa de Reporte Principal.

## Avance 27 enero 2025:
- Trabajando en la ingesta de datos: Materiales Usados, Equipos Usados, Vehículos Usados y Personal de Campo.
- Archivos Excel limpiados y estructurados --> xlsx, csv or db.

## Avance 24 enero 2025:
- Testing GCP Server
- Reunion Ing. Huillca
- Nuevos requirimientos.

## Avance 23 enero 2025:
Intercomunicacion de prueba entre las tres aplicaciones, usando Google Cloud Platform como servidor.

Aplicacion móvil (obra/campo)
![Aplicacion móvil (obra/campo)](https://github.com/user-attachments/assets/b436706a-9f15-4dd9-9573-253b1563213a)

Servidor GCP (nube)
![Servidor GCP (nube)](https://github.com/user-attachments/assets/5c3b9d96-7e7b-406f-abd7-3dda5c6dec66)

Aplicación escritorio (oficina central)
![Aplicación escritorio (oficina central)](https://github.com/user-attachments/assets/2fe87503-0806-4e7d-ad2e-11cefba9eae8)


## Avance 22 enero 2025:
Reestructurando la arquitectura y el desplieque del proyecto. Al final tendrá 3 "programas":
- Aplicación móvil (Android):           sya_operaciones_android.py
- Aplicación de escritorio (Windows):   sya_operaciones_desktop.py
- Servidor Web (Linux):                 sya_operaciones_server.py

El servidor web estará en funcionamiento las 24 hs del día, esperando recibir los formularios provenientes de las aplicaciones android de cada obra.


## Avance 21 enero 2025:
![21-01 avance](https://github.com/user-attachments/assets/100a58bf-47ef-4c38-89eb-3a5f4e7acdfa)

