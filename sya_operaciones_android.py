# Archivo: sya_operaciones_android.py
import requests
from datetime import datetime
from kivy.app import App
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.anchorlayout import AnchorLayout
from kivy.uix.scrollview import ScrollView
from kivy.uix.button import Button
from kivy.uix.textinput import TextInput
from kivy.uix.label import Label
from kivy.uix.spinner import Spinner
from kivy.uix.widget import Widget
from kivy.uix.dropdown import DropDown
from kivy.core.window import Window
from kivy.uix.popup import Popup
from kivy.uix.gridlayout import GridLayout
from kivy.metrics import dp
from kivy.core.window import Window
from kivy.animation import Animation
from kivy.graphics import Color, Rectangle, RoundedRectangle
from kivy.clock import Clock

API_URL = "http://34.67.103.132:5000"
# API_URL = "http://127.0.0.1:5000"

# Modern Color Palette
PRIMARY_COLOR = (0.2, 0.4, 0.8, 1)        # Professional blue
PRIMARY_DARK = (0.15, 0.3, 0.6, 1)        # Darker blue for hover states
SECONDARY_COLOR = (0.3, 0.7, 0.4, 1)      # Modern green
SECONDARY_DARK = (0.2, 0.5, 0.3, 1)       # Darker green
ACCENT_COLOR = (0.9, 0.5, 0.2, 1)         # Warm orange for highlights
DANGER_COLOR = (0.9, 0.3, 0.3, 1)         # Modern red
WARNING_COLOR = (0.95, 0.7, 0.2, 1)       # Amber warning

# Background Colors
BG_COLOR = (0.05, 0.05, 0.08, 1)          # Deep dark background
SURFACE_COLOR = (0.12, 0.12, 0.16, 1)     # Card/surface background
SURFACE_LIGHT = (0.18, 0.18, 0.22, 1)     # Lighter surface
BORDER_COLOR = (0.25, 0.25, 0.3, 1)       # Subtle borders

# Text Colors
TEXT_PRIMARY = (0.95, 0.95, 0.98, 1)      # Primary text (almost white)
TEXT_SECONDARY = (0.7, 0.7, 0.75, 1)      # Secondary text
TEXT_MUTED = (0.5, 0.5, 0.55, 1)          # Muted text
TEXT_ON_PRIMARY = (1, 1, 1, 1)            # Text on primary color

# Legacy color mappings for compatibility
WHITE = TEXT_PRIMARY
BLACK = (0, 0, 0, 1)
GRAY = SURFACE_LIGHT
BTN_COLOR = PRIMARY_COLOR
GREEN = SECONDARY_COLOR
RED = DANGER_COLOR
ITEM_BG_COLOR = SURFACE_COLOR

# Responsive Design System
screen_width = Window.width
screen_height = Window.height
scale_factor = min(screen_width / 1920, screen_height / 1080)

# Typography Scale
FONT_SIZE_DISPLAY = int(120 * scale_factor)      # Large display text
FONT_SIZE_H1 = int(100 * scale_factor)           # Main headings
FONT_SIZE_H2 = int(85 * scale_factor)            # Section headings
FONT_SIZE_H3 = int(75 * scale_factor)            # Subsection headings
FONT_SIZE_BODY = int(65 * scale_factor)          # Body text
FONT_SIZE_CAPTION = int(55 * scale_factor)       # Small text
FONT_SIZE_INPUT = int(70 * scale_factor)         # Input text

# Spacing System
SPACING_XS = int(8 * scale_factor)               # 8px
SPACING_SM = int(12 * scale_factor)              # 12px
SPACING_MD = int(16 * scale_factor)              # 16px
SPACING_LG = int(24 * scale_factor)              # 24px
SPACING_XL = int(32 * scale_factor)              # 32px
SPACING_XXL = int(48 * scale_factor)             # 48px

# Component Dimensions
BUTTON_HEIGHT = int(56 * scale_factor)           # Standard button height
BUTTON_HEIGHT_LG = int(72 * scale_factor)        # Large button height
INPUT_HEIGHT = int(56 * scale_factor)            # Input field height
CARD_RADIUS = int(12 * scale_factor)             # Card border radius
BUTTON_RADIUS = int(8 * scale_factor)            # Button border radius

# Legacy mappings for compatibility
label_font_size = FONT_SIZE_BODY
title_font_size = FONT_SIZE_H2
input_height = INPUT_HEIGHT
button_height = BUTTON_HEIGHT
margin = SPACING_LG
padding = SPACING_MD
text_input_font_size = FONT_SIZE_INPUT

Window.clearcolor = BG_COLOR


# Enhanced UI Component Factory Functions

def create_label(text, style='body', color=None, **kwargs):
    """Create a styled label with consistent typography"""
    font_sizes = {
        'display': FONT_SIZE_DISPLAY,
        'h1': FONT_SIZE_H1,
        'h2': FONT_SIZE_H2,
        'h3': FONT_SIZE_H3,
        'body': FONT_SIZE_BODY,
        'caption': FONT_SIZE_CAPTION
    }

    default_color = TEXT_PRIMARY if color is None else color

    return Label(
        text=text,
        size_hint=(1, None),
        height=dp(INPUT_HEIGHT if style in ['body', 'caption'] else BUTTON_HEIGHT_LG),
        pos_hint={'center_x': 0.5},
        color=default_color,
        font_size=font_sizes.get(style, FONT_SIZE_BODY),
        halign='center',
        valign='middle',
        text_size=(None, None),
        **kwargs
    )


def create_text_input(multiline=False, text="", hint_text="", **kwargs):
    """Create a modern styled text input with improved UX"""
    input_widget = TextInput(
        text=text,
        hint_text=hint_text,
        multiline=multiline,
        size_hint=(1, None),
        height=dp(INPUT_HEIGHT),
        pos_hint={'center_x': 0.5},
        background_color=SURFACE_COLOR,
        foreground_color=TEXT_PRIMARY,
        hint_text_color=TEXT_MUTED,
        cursor_color=PRIMARY_COLOR,
        selection_color=PRIMARY_COLOR + (0.3,),  # Semi-transparent selection
        font_size=FONT_SIZE_INPUT,
        padding=(dp(SPACING_MD), dp(SPACING_SM)),
        **kwargs
    )

    # Add focus styling
    def on_focus(instance, focused):
        if focused:
            instance.background_color = SURFACE_LIGHT
        else:
            instance.background_color = SURFACE_COLOR

    input_widget.bind(focus=on_focus)
    return input_widget


def create_button(text, style='primary', size=None, on_press=None, **kwargs):
    """Create a modern styled button with consistent design"""
    styles = {
        'primary': {'bg': PRIMARY_COLOR, 'text': TEXT_ON_PRIMARY},
        'secondary': {'bg': SECONDARY_COLOR, 'text': TEXT_ON_PRIMARY},
        'danger': {'bg': DANGER_COLOR, 'text': TEXT_ON_PRIMARY},
        'warning': {'bg': WARNING_COLOR, 'text': BLACK},
        'surface': {'bg': SURFACE_LIGHT, 'text': TEXT_PRIMARY},
        'outline': {'bg': (0, 0, 0, 0), 'text': PRIMARY_COLOR}  # Transparent background
    }

    button_style = styles.get(style, styles['primary'])
    default_size = size or (dp(200), dp(BUTTON_HEIGHT))

    button = Button(
        text=text,
        size_hint=(None, None),
        size=default_size,
        pos_hint={'center_x': 0.5},
        background_color=button_style['bg'],
        color=button_style['text'],
        font_size=FONT_SIZE_BODY,
        bold=True,
        on_press=on_press,
        **kwargs
    )

    # Add hover effect
    def on_state(instance, state):
        if state == 'down':
            if style == 'primary':
                instance.background_color = PRIMARY_DARK
            elif style == 'secondary':
                instance.background_color = SECONDARY_DARK
            else:
                # Darken the color slightly for other styles
                original = button_style['bg']
                instance.background_color = tuple(max(0, c - 0.1) for c in original[:3]) + (original[3],)
        else:
            instance.background_color = button_style['bg']

    button.bind(state=on_state)
    return button


def create_card(content_widget, padding=None, **kwargs):
    """Create a modern card container with consistent styling"""
    card_padding = padding or SPACING_MD

    card = BoxLayout(
        orientation='vertical',
        size_hint_y=None,
        padding=dp(card_padding),
        spacing=dp(SPACING_SM),
        **kwargs
    )

    # Add background
    with card.canvas.before:
        Color(*SURFACE_COLOR)
        card.bg_rect = Rectangle(pos=card.pos, size=card.size)

    def update_bg(instance, value):
        card.bg_rect.pos = instance.pos
        card.bg_rect.size = instance.size

    card.bind(pos=update_bg, size=update_bg)
    card.add_widget(content_widget)

    return card


class MainScreen(BoxLayout):
    def __init__(self, main_app, **kwargs):
        super().__init__(**kwargs)
        self.main_app = main_app
        self.orientation = 'vertical'
        self.padding = dp(SPACING_XXL)
        self.spacing = dp(SPACING_XL)

        # Create main container with modern styling
        self.create_header()
        self.create_navigation_cards()
        self.create_footer()

    def create_header(self):
        """Create modern header with app branding"""
        header_container = BoxLayout(
            orientation='vertical',
            size_hint_y=None,
            height=dp(200),
            spacing=dp(SPACING_MD)
        )

        # App title with modern typography
        app_title = create_label(
            'SYA OPERACIONES',
            style='display',
            color=PRIMARY_COLOR
        )
        app_title.bold = True

        # Subtitle
        subtitle = create_label(
            'Sistema de Gestión de Operaciones',
            style='h3',
            color=TEXT_SECONDARY
        )

        header_container.add_widget(app_title)
        header_container.add_widget(subtitle)
        self.add_widget(header_container)

    def create_navigation_cards(self):
        """Create modern navigation cards"""
        nav_container = BoxLayout(
            orientation='vertical',
            spacing=dp(SPACING_LG),
            size_hint_y=None
        )
        nav_container.bind(minimum_height=nav_container.setter('height'))

        # Create cards for each main function
        reporte_card = self.create_nav_card(
            title='REPORTE DIARIO DE OBRA',
            description='Registrar actividades diarias, materiales, equipos y personal',
            icon='📋',
            on_press=self.show_reporte_diario_screen,
            color=PRIMARY_COLOR
        )

        requerimiento_card = self.create_nav_card(
            title='REQUERIMIENTO DE MATERIALES',
            description='Solicitar materiales y equipos necesarios para las operaciones',
            icon='📦',
            on_press=self.show_requerimiento_materiales_screen,
            color=SECONDARY_COLOR
        )

        nav_container.add_widget(reporte_card)
        nav_container.add_widget(requerimiento_card)
        self.add_widget(nav_container)

    def create_nav_card(self, title, description, icon, on_press, color):
        """Create a modern navigation card"""
        # Main card container
        card_container = BoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(120),
            spacing=dp(SPACING_MD),
            padding=dp(SPACING_LG)
        )

        # Add card background with rounded corners
        with card_container.canvas.before:
            Color(*SURFACE_COLOR)
            card_container.bg_rect = Rectangle(pos=card_container.pos, size=card_container.size)

        def update_card_bg(instance, value):
            card_container.bg_rect.pos = instance.pos
            card_container.bg_rect.size = instance.size

        card_container.bind(pos=update_card_bg, size=update_card_bg)

        # Icon section
        icon_container = BoxLayout(
            orientation='vertical',
            size_hint_x=None,
            width=dp(80),
            padding=(0, dp(SPACING_SM))
        )

        icon_label = Label(
            text=icon,
            font_size=dp(48),
            size_hint_y=None,
            height=dp(60),
            color=color
        )
        icon_container.add_widget(icon_label)

        # Content section
        content_container = BoxLayout(
            orientation='vertical',
            spacing=dp(SPACING_XS),
            padding=(0, dp(SPACING_SM))
        )

        title_label = create_label(
            title,
            style='h3',
            color=TEXT_PRIMARY
        )
        title_label.bold = True
        title_label.halign = 'left'
        title_label.text_size = (None, None)

        desc_label = create_label(
            description,
            style='caption',
            color=TEXT_SECONDARY
        )
        desc_label.halign = 'left'
        desc_label.text_size = (dp(400), None)
        desc_label.height = dp(40)

        content_container.add_widget(title_label)
        content_container.add_widget(desc_label)

        # Action button
        action_button = create_button(
            'ACCEDER',
            style='primary',
            size=(dp(120), dp(40)),
            on_press=on_press
        )
        action_button.size_hint = (None, None)

        # Assemble card
        card_container.add_widget(icon_container)
        card_container.add_widget(content_container)
        card_container.add_widget(action_button)

        # Add hover effect
        def on_touch_down(instance, touch):
            if instance.collide_point(*touch.pos):
                with instance.canvas.before:
                    Color(*SURFACE_LIGHT)
                    instance.bg_rect = Rectangle(pos=instance.pos, size=instance.size)
                return True
            return False

        def on_touch_up(instance, touch):
            with instance.canvas.before:
                Color(*SURFACE_COLOR)
                instance.bg_rect = Rectangle(pos=instance.pos, size=instance.size)

        card_container.bind(on_touch_down=on_touch_down, on_touch_up=on_touch_up)

        return card_container

    def create_footer(self):
        """Create footer with app info"""
        footer_container = BoxLayout(
            orientation='vertical',
            size_hint_y=None,
            height=dp(60),
            spacing=dp(SPACING_XS)
        )

        version_label = create_label(
            'Versión 2.0 - Sistema Optimizado',
            style='caption',
            color=TEXT_MUTED
        )

        footer_container.add_widget(Widget())  # Spacer
        footer_container.add_widget(version_label)
        self.add_widget(footer_container)

    def show_reporte_diario_screen(self, instance):
        self.main_app.show_reporte_diario_screen()

    def show_requerimiento_materiales_screen(self, instance):
        self.main_app.show_requerimiento_materiales_screen()


class CustomDropDown(DropDown):
    def __init__(self, **kwargs):
        super(CustomDropDown, self).__init__(**kwargs)
        self.auto_width = False
        self.width = 400


class MaterialEntry:
    def __init__(self, nombre, unidad, cantidad):
        self.nombre = nombre
        self.unidad = unidad
        self.cantidad = cantidad

    def to_dict(self):
        return {"nombre": self.nombre, "unidad": self.unidad, "cantidad": self.cantidad}


class EquipoEntry:  # Class for Reporte Diario Obra
    def __init__(self, nombre, cantidad, propiedad):
        self.nombre = nombre
        self.cantidad = cantidad
        self.propiedad = propiedad

    def to_dict(self):
        return {"nombre": self.nombre, "cantidad": self.cantidad, "propiedad": self.propiedad}


class RequerimientoEquipoEntry:  # New class for Requerimiento Materiales
    def __init__(self, nombre, unidad, cantidad):
        self.nombre = nombre
        self.unidad = unidad  # In Requerimiento, 'propiedad' field from CSV is used as 'unidad' in UI
        self.cantidad = cantidad

    def to_dict(self):
        return {"nombre": self.nombre, "unidad": self.unidad,
                "cantidad": self.cantidad}  # Use "unidad" as key in dict


class VehiculoEntry:
    def __init__(self, nombre, placa, propiedad):
        self.nombre = nombre
        self.placa = placa
        self.propiedad = propiedad

    def to_dict(self):
        return {"nombre": self.nombre, "placa": self.placa, "propiedad": self.propiedad}


class PersonalEntry:
    def __init__(self, nombre_completo, categoria, horas_extras):
        self.nombre_completo = nombre_completo
        self.categoria = categoria
        self.horas_extras = horas_extras

    def to_dict(self):
        return {"nombre_completo": self.nombre_completo, "categoria": self.categoria, "horas_extras": self.horas_extras}


class MaterialSelectionPopup(Popup):
    def __init__(self, add_callback, main_app, **kwargs):
        self.add_callback = add_callback
        self.main_app = main_app
        kwargs.pop('add_callback', None)
        kwargs.pop('main_app', None)
        super(MaterialSelectionPopup, self).__init__(**kwargs)

        # Modern popup styling
        self.title = "Selección de Materiales"
        self.size_hint = (0.95, 0.9)
        self.background_color = SURFACE_COLOR
        self.separator_height = 0
        self.title_color = TEXT_PRIMARY
        self.title_size = FONT_SIZE_H3
        self.is_new_material = False

        # Main layout with modern spacing
        layout = BoxLayout(
            orientation='vertical',
            spacing=dp(SPACING_LG),
            padding=dp(SPACING_LG)
        )

        # Enhanced search section
        search_section = self.create_search_section()
        layout.add_widget(search_section)

        # Materials list with modern styling
        materials_section = self.create_materials_section()
        layout.add_widget(materials_section)

        # Selection form with improved UX
        selection_section = self.create_selection_section()
        layout.add_widget(selection_section)

        # Selected materials display
        selected_section = self.create_selected_section()
        layout.add_widget(selected_section)

        # Action buttons with modern styling
        buttons_section = self.create_buttons_section()
        layout.add_widget(buttons_section)

        self.content = layout
        self.search_input.bind(text=self.on_search_text)
        self.update_selected_materials_display()

    def create_search_section(self):
        """Create modern search section"""
        search_container = BoxLayout(
            orientation='vertical',
            size_hint_y=None,
            height=dp(80),
            spacing=dp(SPACING_SM)
        )

        search_label = create_label("Buscar Material", style='h3')
        search_label.halign = 'left'

        self.search_input = create_text_input(
            hint_text='Escriba el nombre del material...',
            multiline=False
        )

        search_container.add_widget(search_label)
        search_container.add_widget(self.search_input)
        return search_container

    def create_materials_section(self):
        """Create materials list section"""
        materials_container = BoxLayout(
            orientation='vertical',
            size_hint_y=0.25,
            spacing=dp(SPACING_SM)
        )

        materials_label = create_label("Materiales Disponibles", style='h3')
        materials_label.halign = 'left'

        scroll = ScrollView()
        self.materials_list = GridLayout(
            cols=1,
            spacing=dp(SPACING_XS),
            size_hint_y=None,
            padding=dp(SPACING_SM)
        )
        self.materials_list.bind(minimum_height=self.materials_list.setter('height'))
        scroll.add_widget(self.materials_list)

        materials_container.add_widget(materials_label)
        materials_container.add_widget(scroll)
        return materials_container

    def create_selection_section(self):
        """Create selection form section"""
        selection_container = BoxLayout(
            orientation='vertical',
            size_hint_y=None,
            height=dp(180),
            spacing=dp(SPACING_SM)
        )

        selection_label = create_label("Detalles del Material", style='h3')
        selection_label.halign = 'left'

        # Material name display
        self.material_name = create_label("Seleccione un material", style='body', color=TEXT_SECONDARY)
        self.material_name.halign = 'left'

        # Form fields
        form_layout = GridLayout(cols=2, spacing=dp(SPACING_MD), size_hint_y=None, height=dp(120))

        form_layout.add_widget(create_label("Unidad:", style='body'))
        self.unit_input = create_text_input(hint_text='Ej: kg, m³, unidades')
        form_layout.add_widget(self.unit_input)

        form_layout.add_widget(create_label("Cantidad:", style='body'))
        self.quantity_input = create_text_input(hint_text='Ej: 10.5', input_filter='float')
        form_layout.add_widget(self.quantity_input)

        selection_container.add_widget(selection_label)
        selection_container.add_widget(self.material_name)
        selection_container.add_widget(form_layout)
        return selection_container

    def create_selected_section(self):
        """Create selected materials section"""
        selected_container = BoxLayout(
            orientation='vertical',
            size_hint_y=0.35,
            spacing=dp(SPACING_SM)
        )

        selected_label = create_label("Materiales Seleccionados", style='h3')
        selected_label.halign = 'left'

        self.selected_materials_scroll = ScrollView()
        self.selected_materials_grid = GridLayout(
            cols=1,
            spacing=dp(SPACING_SM),
            size_hint_y=None,
            padding=dp(SPACING_SM)
        )
        self.selected_materials_grid.bind(minimum_height=self.selected_materials_grid.setter('height'))
        self.selected_materials_scroll.add_widget(self.selected_materials_grid)

        selected_container.add_widget(selected_label)
        selected_container.add_widget(self.selected_materials_scroll)
        return selected_container

    def create_buttons_section(self):
        """Create action buttons section"""
        buttons_container = BoxLayout(
            orientation='horizontal',
            size_hint_y=None,
            height=dp(BUTTON_HEIGHT + SPACING_MD),
            spacing=dp(SPACING_MD)
        )

        cancel_button = create_button('Cancelar', style='danger', on_press=self.dismiss)
        add_button = create_button('Agregar', style='secondary', on_press=self.add_material)
        finish_button = create_button('Terminar', style='primary', on_press=self.on_finish)

        buttons_container.add_widget(cancel_button)
        buttons_container.add_widget(add_button)
        buttons_container.add_widget(finish_button)
        return buttons_container

    def on_finish(self, instance):
        self.dismiss()
        if self.main_app.materiales_seleccionados:
            self.main_app.materiales_button.background_color = BTN_COLOR

    def on_search_text(self, instance, value):
        self.materials_list.clear_widgets()
        if not value:
            return

        filtered_materials = [m['nombre_material'] for m in self.main_app.materiales_data if
                              value.lower() in m['nombre_material'].lower()]

        for material in filtered_materials:
            btn = create_button(
                material,
                style='surface',
                size=(dp(300), dp(40)),
                on_press=lambda x, mat=material: self.select_material(mat)
            )
            btn.size_hint_y = None
            btn.height = dp(40)
            self.materials_list.add_widget(btn)

        unique_material_names = [m['nombre_material'] for m in self.main_app.materiales_data]
        if value and value not in filtered_materials and value not in unique_material_names:
            new_btn = create_button(
                f"+ Agregar: {value}",
                style='secondary',
                size=(dp(300), dp(40)),
                on_press=lambda x: self.select_new_material(value)
            )
            new_btn.size_hint_y = None
            new_btn.height = dp(40)
            self.materials_list.add_widget(new_btn)

    def select_material(self, material_name):
        self.material_name.text = f"Material seleccionado: {material_name}"
        self.material_name.color = PRIMARY_COLOR
        self.unit_input.readonly = False
        self.unit_input.text = ""
        self.is_new_material = False

        # Find and populate unit from existing data
        for material in self.main_app.materiales_data:
            if material['nombre_material'] == material_name:
                unit = material['unidad']
                self.unit_input.text = unit
                break

        self.materials_list.clear_widgets()
        self.search_input.text = material_name

    def select_new_material(self, material_name):
        self.material_name.text = f"Nuevo material: {material_name}"
        self.material_name.color = SECONDARY_COLOR
        self.unit_input.readonly = False
        self.unit_input.text = ""
        self.is_new_material = True
        self.materials_list.clear_widgets()
        self.search_input.text = material_name

    def add_material(self, *args):
        # Extract material name from display text
        if "Material seleccionado:" in self.material_name.text:
            material_name = self.material_name.text.replace("Material seleccionado: ", "")
        elif "Nuevo material:" in self.material_name.text:
            material_name = self.material_name.text.replace("Nuevo material: ", "")
        else:
            return  # No material selected

        unit = self.unit_input.text
        quantity = self.quantity_input.text

        if not all([material_name, unit, quantity]):
            return

        try:
            quantity = float(quantity)
            self.add_callback(material_name, unit, quantity, self.is_new_material)

            # Clear form
            self.search_input.text = ''
            self.unit_input.text = ''
            self.quantity_input.text = ''
            self.material_name.text = 'Seleccione un material'
            self.material_name.color = TEXT_SECONDARY
            self.is_new_material = False
            self.update_selected_materials_display()
        except ValueError:
            print("Por favor ingrese una cantidad válida")

    def update_selected_materials_display(self):
        self.selected_materials_grid.clear_widgets()

        if not self.main_app.materiales_seleccionados:
            # Show empty state
            empty_label = create_label(
                "No hay materiales seleccionados",
                style='caption',
                color=TEXT_MUTED
            )
            empty_label.halign = 'center'
            self.selected_materials_grid.add_widget(empty_label)
            return

        for material in self.main_app.materiales_seleccionados:
            # Create modern material card
            card = BoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(80),
                spacing=dp(SPACING_MD),
                padding=dp(SPACING_MD)
            )

            # Add modern card background
            with card.canvas.before:
                Color(*SURFACE_LIGHT)
                card.bg_rect = Rectangle(pos=card.pos, size=card.size)

            def update_card_bg(instance, value):
                card.bg_rect.pos = instance.pos
                card.bg_rect.size = instance.size

            card.bind(pos=update_card_bg, size=update_card_bg)

            # Material icon
            icon_container = BoxLayout(
                orientation='vertical',
                size_hint_x=None,
                width=dp(50),
                padding=(0, dp(SPACING_SM))
            )

            icon_label = Label(
                text='📦',
                font_size=dp(24),
                size_hint_y=None,
                height=dp(30),
                color=PRIMARY_COLOR
            )
            icon_container.add_widget(icon_label)

            # Content section
            content_layout = BoxLayout(
                orientation='vertical',
                spacing=dp(SPACING_XS),
                padding=(0, dp(SPACING_XS))
            )

            # Material name
            nombre_label = create_label(
                material.nombre,
                style='body',
                color=TEXT_PRIMARY
            )
            nombre_label.bold = True
            nombre_label.halign = 'left'
            nombre_label.text_size = (dp(300), None)
            nombre_label.size_hint_y = 0.6

            # Quantity and unit
            detalles_label = create_label(
                f"{material.cantidad} {material.unidad}",
                style='caption',
                color=TEXT_SECONDARY
            )
            detalles_label.halign = 'left'
            detalles_label.text_size = (dp(300), None)
            detalles_label.size_hint_y = 0.4

            content_layout.add_widget(nombre_label)
            content_layout.add_widget(detalles_label)

            # Delete button with modern styling
            delete_btn = create_button(
                '×',
                style='danger',
                size=(dp(40), dp(40)),
                on_press=lambda btn, m=material, card_widget=card: self.remove_material_from_popup(m, card_widget)
            )
            delete_btn.size_hint = (None, None)
            delete_btn.font_size = dp(20)
            delete_btn.bold = True

            # Assemble card
            card.add_widget(icon_container)
            card.add_widget(content_layout)
            card.add_widget(delete_btn)
            self.selected_materials_grid.add_widget(card)

    def remove_material_from_popup(self, material_entry, material_layout):
        self.main_app.materiales_seleccionados.remove(material_entry)
        self.selected_materials_grid.remove_widget(material_layout)
        self.update_selected_materials_display()


class EquipoSelectionPopup(Popup):  # Popup for Reporte Diario Obra - No changes needed
    def __init__(self, add_callback, main_app, **kwargs):
        self.add_callback = add_callback
        self.main_app = main_app
        kwargs.pop('add_callback', None)
        kwargs.pop('main_app', None)
        super(EquipoSelectionPopup, self).__init__(**kwargs)
        self.title = "Selección de Equipos"
        self.size_hint = (0.9, 0.9)
        self.is_new_equipo = False

        layout = BoxLayout(orientation='vertical', spacing=dp(10), padding=dp(10))

        search_layout = BoxLayout(size_hint_y=None, height=dp(40))
        self.search_input = TextInput(
            hint_text='Buscar equipo...',
            multiline=False,
            size_hint=(1, None),
            height=dp(40)
        )
        search_layout.add_widget(self.search_input)

        scroll = ScrollView(size_hint_y=0.3)
        self.equipos_list = GridLayout(cols=1, spacing=dp(5), size_hint_y=None, padding=dp(5))
        self.equipos_list.bind(minimum_height=self.equipos_list.setter('height'))
        scroll.add_widget(self.equipos_list)

        self.selected_equipos_scroll = ScrollView(size_hint_y=0.4)
        self.selected_equipos_grid = GridLayout(cols=1, spacing=dp(5), size_hint_y=None, padding=dp(5))
        self.selected_equipos_grid.bind(minimum_height=self.selected_equipos_grid.setter('height'))
        self.selected_equipos_scroll.add_widget(
            self.selected_equipos_grid)  # Corrected line: adding grid, not scroll

        selection_layout = GridLayout(cols=2, spacing=dp(10), size_hint_y=0.3, height=dp(120), padding=dp(10))

        self.equipo_name = Label(text="Equipo: ")
        self.propiedad_input = TextInput(hint_text='Propiedad', multiline=False, size_hint_y=None, height=dp(40))
        self.quantity_input = TextInput(hint_text='Cantidad', multiline=False, input_filter='float', size_hint_y=None,
                                        height=dp(40))

        selection_layout.add_widget(Label(text="Propiedad:"))
        selection_layout.add_widget(self.propiedad_input)
        selection_layout.add_widget(Label(text="Cantidad:"))
        selection_layout.add_widget(self.quantity_input)

        buttons_layout = BoxLayout(size_hint_y=None, height=dp(50), spacing=dp(10))

        cancel_button = Button(text='Cancelar', size_hint_x=0.5, background_color=RED)
        add_button = Button(text='Agregar', size_hint_x=0.5, background_color=GREEN)
        finish_button = Button(text='Terminar', size_hint_x=0.5, background_color=BTN_COLOR)

        cancel_button.bind(on_release=self.dismiss)
        add_button.bind(on_release=self.add_equipo)
        finish_button.bind(on_release=self.on_finish)

        buttons_layout.add_widget(cancel_button)
        buttons_layout.add_widget(add_button)
        buttons_layout.add_widget(finish_button)

        layout.add_widget(search_layout)
        layout.add_widget(scroll)
        layout.add_widget(selection_layout)
        layout.add_widget(self.selected_equipos_scroll)
        layout.add_widget(buttons_layout)

        self.content = layout
        self.search_input.bind(text=self.on_search_text)
        self.update_selected_equipos_display()

    def on_finish(self, instance):
        self.dismiss()
        if self.main_app.equipos_seleccionados:
            self.main_app.equipos_button.background_color = BTN_COLOR

    def on_search_text(self, instance, value):  # For Reporte Diario Obra - No changes needed
        self.equipos_list.clear_widgets()
        if not value:
            return

        filtered_equipos = [e['nombre_equipo'] for e in self.main_app.equipos_data if
                            value.lower() in e['nombre_equipo'].lower()]

        for equipo in filtered_equipos:
            btn = Button(text=equipo, size_hint_y=None, height=dp(40), background_color=GRAY)
            btn.bind(on_release=lambda btn: self.select_equipo(btn.text))
            self.equipos_list.add_widget(btn)

        unique_equipo_names = [e['nombre_equipo'] for e in self.main_app.equipos_data]
        if value and value not in filtered_equipos and value not in unique_equipo_names:
            new_btn = Button(text=f"Agregar nuevo equipo: {value}", size_hint_y=None, height=dp(40),
                             background_color=GREEN)
            new_btn.bind(on_release=lambda btn: self.select_new_equipo(value))
            self.equipos_list.add_widget(new_btn)

    def select_equipo(self, equipo_name):  # For Reporte Diario Obra - no changes needed
        self.equipo_name.text = f"Equipo: {equipo_name}"
        self.propiedad_input.readonly = False
        self.propiedad_input.text = ""
        self.is_new_equipo = False
        for equipo in self.main_app.equipos_data:
            if equipo['nombre_equipo'] == equipo_name:
                propiedad = equipo['propiedad']
                self.propiedad_input.text = propiedad
                break
        self.equipos_list.clear_widgets()
        self.search_input.text = equipo_name

    def select_new_equipo(self, equipo_name):  # For Reporte Diario Obra - no changes needed
        self.equipo_name.text = f"Equipo: {equipo_name}"
        self.propiedad_input.readonly = False
        self.propiedad_input.text = ""
        self.is_new_equipo = True
        self.equipos_list.clear_widgets()
        self.search_input.text = equipo_name

    def add_equipo(self, *args):  # For Reporte Diario Obra - no changes needed
        equipo_name = self.equipo_name.text.replace("Equipo: ", "")
        propiedad = self.propiedad_input.text
        cantidad = self.quantity_input.text

        if not all([equipo_name, propiedad, cantidad]):
            return

        try:
            cantidad = float(cantidad)
            self.add_callback(equipo_name, cantidad, propiedad, self.is_new_equipo)
            self.search_input.text = ''
            self.propiedad_input.text = ''
            self.quantity_input.text = ''
            self.equipo_name.text = 'Equipo: '
            self.is_new_equipo = False
            self.update_selected_equipos_display()
        except ValueError:
            print("Por favor ingrese una cantidad válida")

    def update_selected_equipos_display(self):  # For Reporte Diario Obra - no changes needed
        self.selected_equipos_grid.clear_widgets()
        grid = self.selected_equipos_grid
        grid.spacing = dp(5)
        grid.padding = dp(5)

        for equipo in self.main_app.equipos_seleccionados:
            card = BoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(80),
                spacing=dp(5),
                padding=dp(5),
            )
            with card.canvas.before:
                Color(*ITEM_BG_COLOR)
                Rectangle(pos=card.pos, size=card.size)

            text_layout = BoxLayout(
                orientation='vertical',
                size_hint_x=0.8,
                padding=[0, 0, 0, 0]
            )

            nombre_label = Label(
                text=f"[b]{equipo.nombre}[/b]",
                markup=True,
                font_size=label_font_size,
                halign='left',
                valign='middle',
                color=WHITE,
                size_hint_y=0.5,
                text_size=(None, None),
                padding=[0, 0]
            )

            detalles_label = Label(
                text=f"[size={int(label_font_size)}]{equipo.cantidad} und • {equipo.propiedad}[/size]",
                markup=True,
                halign='left',
                valign='middle',
                color=WHITE,
                size_hint_y=0.5,
                text_size=(Window.width * 0.6, None),
                padding=[0, 0]
            )

            text_layout.add_widget(nombre_label)
            text_layout.add_widget(detalles_label)

            delete_btn = Button(
                text='×',
                font_size=label_font_size,
                bold=True,
                size_hint_x=None,
                width=dp(40),
                background_color=RED,
                background_normal='',
                on_press=lambda btn, e=equipo, card_widget=card: self.remove_equipo_from_popup(e, card_widget)
            )

            card.add_widget(text_layout)
            card.add_widget(delete_btn)
            self.selected_equipos_grid.add_widget(card)

    def remove_equipo_from_popup(self, equipo_entry, equipo_layout):  # For Reporte Diario Obra - no changes needed
        self.main_app.equipos_seleccionados.remove(equipo_entry)
        self.selected_equipos_grid.remove_widget(equipo_layout)
        self.update_selected_equipos_display()


class VehiculoSelectionPopup(Popup):
    def __init__(self, add_callback, main_app, **kwargs):
        self.add_callback = add_callback
        self.main_app = main_app
        kwargs.pop('add_callback', None)
        kwargs.pop('main_app', None)
        super(VehiculoSelectionPopup, self).__init__(**kwargs)
        self.title = "Selección de Vehículos"
        self.size_hint = (0.9, 0.9)
        self.is_new_vehiculo = False

        layout = BoxLayout(orientation='vertical', spacing=dp(10), padding=dp(10))

        search_layout = BoxLayout(size_hint_y=None, height=dp(40))
        self.search_input = TextInput(
            hint_text='Buscar vehículo...',
            multiline=False,
            size_hint=(1, None),
            height=dp(40)
        )
        search_layout.add_widget(self.search_input)

        scroll = ScrollView(size_hint_y=0.3)  # 30% del espacio
        self.vehiculos_list = GridLayout(cols=1, spacing=dp(5), size_hint_y=None, padding=dp(5))
        self.vehiculos_list.bind(minimum_height=self.vehiculos_list.setter('height'))
        scroll.add_widget(self.vehiculos_list)

        self.selected_vehiculos_scroll = ScrollView(size_hint_y=0.4)  # 40% del espacio
        self.selected_vehiculos_grid = GridLayout(cols=1, spacing=dp(5), size_hint_y=None, padding=dp(5))
        self.selected_vehiculos_grid.bind(minimum_height=self.selected_vehiculos_grid.setter('height'))
        self.selected_vehiculos_scroll.add_widget(self.selected_vehiculos_grid)

        selection_layout = GridLayout(cols=2, spacing=dp(10), size_hint_y=0.3, height=dp(120),
                                      padding=dp(10))  # 30% del espacio

        self.vehiculo_name = Label(text="Vehículo: ")
        self.propiedad_input = TextInput(hint_text='Propiedad', multiline=False, size_hint_y=None, height=dp(40))
        self.placa_input = TextInput(hint_text='Placa', multiline=False, size_hint_y=None, height=dp(40))

        selection_layout.add_widget(Label(text="Propiedad:"))
        selection_layout.add_widget(self.propiedad_input)
        selection_layout.add_widget(Label(text="Placa:"))
        selection_layout.add_widget(self.placa_input)

        buttons_layout = BoxLayout(size_hint_y=None, height=dp(50), spacing=dp(10))

        cancel_button = Button(text='Cancelar', size_hint_x=0.5, background_color=RED)
        add_button = Button(text='Agregar', size_hint_x=0.5, background_color=GREEN)
        finish_button = Button(text='Terminar', size_hint_x=0.5, background_color=BTN_COLOR)

        cancel_button.bind(on_release=self.dismiss)
        add_button.bind(on_release=self.add_vehiculo)
        finish_button.bind(on_release=self.on_finish)

        buttons_layout.add_widget(cancel_button)
        buttons_layout.add_widget(add_button)
        buttons_layout.add_widget(finish_button)

        layout.add_widget(search_layout)
        layout.add_widget(scroll)
        layout.add_widget(selection_layout)
        layout.add_widget(self.selected_vehiculos_scroll)
        layout.add_widget(buttons_layout)

        self.content = layout
        self.search_input.bind(text=self.on_search_text)
        self.update_selected_vehiculos_display()

    def on_finish(self, instance):
        self.dismiss()
        if self.main_app.vehiculos_seleccionados:
            self.main_app.vehiculos_button.background_color = BTN_COLOR

    def on_search_text(self, instance, value):
        self.vehiculos_list.clear_widgets()
        if not value:
            return

        filtered_vehiculos = [v['nombre_vehiculo'] for v in self.main_app.vehiculos_data if
                              value.lower() in v['nombre_vehiculo'].lower()]

        for vehiculo in filtered_vehiculos:
            btn = Button(text=vehiculo, size_hint_y=None, height=dp(40), background_color=GRAY)
            btn.bind(on_release=lambda btn: self.select_vehiculo(btn.text))
            self.vehiculos_list.add_widget(btn)

        unique_vehiculo_names = [v['nombre_vehiculo'] for v in self.main_app.vehiculos_data]
        if value and value not in filtered_vehiculos and value not in unique_vehiculo_names:
            new_btn = Button(text=f"Agregar nuevo vehículo: {value}", size_hint_y=None, height=dp(40), background_color=GREEN)
            new_btn.bind(on_release=lambda btn: self.select_new_vehiculo(value))
            self.vehiculos_list.add_widget(new_btn)

    def select_vehiculo(self, vehiculo_name):
        self.vehiculo_name.text = f"Vehículo: {vehiculo_name}"
        self.placa_input.readonly = False
        self.propiedad_input.readonly = False
        self.placa_input.text = ""
        self.propiedad_input.text = ""
        self.is_new_vehiculo = False
        for vehiculo in self.main_app.vehiculos_data:
            if vehiculo['nombre_vehiculo'] == vehiculo_name:
                placa = vehiculo['placa']
                propiedad = vehiculo['propiedad']
                self.placa_input.text = placa
                self.propiedad_input.text = propiedad
                break
        self.vehiculos_list.clear_widgets()
        self.search_input.text = vehiculo_name

    def select_new_vehiculo(self, vehiculo_name):
        self.vehiculo_name.text = f"Vehículo: {vehiculo_name}"
        self.placa_input.readonly = False
        self.propiedad_input.readonly = False
        self.placa_input.text = ""
        self.propiedad_input.text = ""
        self.is_new_vehiculo = True
        self.vehiculos_list.clear_widgets()
        self.search_input.text = vehiculo_name

    def add_vehiculo(self, *args):
        vehiculo_name = self.vehiculo_name.text.replace("Vehículo: ", "")
        placa = self.placa_input.text
        propiedad = self.propiedad_input.text

        if not all([vehiculo_name, placa, propiedad]):
            return

        self.add_callback(vehiculo_name, placa, propiedad, self.is_new_vehiculo)
        self.search_input.text = ''
        self.placa_input.text = ''
        self.propiedad_input.text = ''
        self.vehiculo_name.text = 'Vehículo: '
        self.is_new_vehiculo = False
        self.update_selected_vehiculos_display()

    def update_selected_vehiculos_display(self):
        self.selected_vehiculos_grid.clear_widgets()
        grid = self.selected_vehiculos_grid
        grid.spacing = dp(5)
        grid.padding = dp(5)

        for vehiculo in self.main_app.vehiculos_seleccionados:
            card = BoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(80),
                spacing=dp(5),
                padding=dp(5),
            )
            with card.canvas.before:
                Color(*ITEM_BG_COLOR)
                Rectangle(pos=card.pos, size=card.size)

            text_layout = BoxLayout(
                orientation='vertical',
                size_hint_x=0.8,
                padding=[0,0,0,0]
            )

            nombre_label = Label(
                text=f"[b]{vehiculo.nombre}[/b]",
                markup=True,
                font_size=label_font_size,
                halign='left',
                valign='middle',
                color=WHITE,
                size_hint_y=0.5,
                text_size=(None, None),
                padding=[0,0]
            )

            detalles_label = Label(
                text=f"[size={int(label_font_size)}]{vehiculo.placa} • {vehiculo.propiedad}[/size]",
                markup=True,
                halign='left',
                valign='middle',
                color=WHITE,
                size_hint_y=0.5,
                text_size=(Window.width * 0.6, None),
                padding=[0,0]
            )

            text_layout.add_widget(nombre_label)
            text_layout.add_widget(detalles_label)

            delete_btn = Button(
                text='×',
                font_size=label_font_size,
                bold=True,
                size_hint_x=None,
                width=dp(40),
                background_color=RED,
                background_normal='',
                on_press=lambda btn, v=vehiculo, card_widget=card: self.remove_vehiculo_from_popup(v, card_widget)
            )

            card.add_widget(text_layout)
            card.add_widget(delete_btn)
            self.selected_vehiculos_grid.add_widget(card)

    def remove_vehiculo_from_popup(self, vehiculo_entry, vehiculo_layout):
        self.main_app.vehiculos_seleccionados.remove(vehiculo_entry)
        self.selected_vehiculos_grid.remove_widget(vehiculo_layout)
        self.update_selected_vehiculos_display()


class PersonalSelectionPopup(Popup):
    def __init__(self, add_callback, main_app, **kwargs):
        self.add_callback = add_callback
        self.main_app = main_app
        kwargs.pop('add_callback', None)
        kwargs.pop('main_app', None)
        super(PersonalSelectionPopup, self).__init__(**kwargs)
        self.title = "Selección de Personal de Campo"
        self.size_hint = (0.9, 0.9)
        self.is_new_personal = False

        layout = BoxLayout(orientation='vertical', spacing=dp(10), padding=dp(10))

        search_layout = BoxLayout(size_hint_y=None, height=dp(40))
        self.search_input = TextInput(
            hint_text='Buscar personal...',
            multiline=False,
            size_hint=(1, None),
            height=dp(40)
        )
        search_layout.add_widget(self.search_input)

        scroll = ScrollView(size_hint_y=0.3)  # 30% del espacio
        self.personal_list = GridLayout(cols=1, spacing=dp(5), size_hint_y=None, padding=dp(5))
        self.personal_list.bind(minimum_height=self.personal_list.setter('height'))
        scroll.add_widget(self.personal_list)

        self.selected_personal_scroll = ScrollView(size_hint_y=0.4)  # 40% del espacio
        self.selected_personal_grid = GridLayout(cols=1, spacing=dp(5), size_hint_y=None, padding=dp(5))
        self.selected_personal_grid.bind(minimum_height=self.selected_personal_grid.setter('height'))
        self.selected_personal_scroll.add_widget(self.selected_personal_grid)

        selection_layout = GridLayout(cols=2, spacing=dp(10), size_hint_y=0.3, height=dp(120), padding=dp(10))  # 30% del espacio

        self.personal_name = Label(text="Personal: ")
        self.categoria_input = TextInput(hint_text='Categoría', multiline=False, size_hint_y=None, height=dp(40))
        self.horas_extras_input = TextInput(hint_text='Horas Extras', multiline=False, input_filter='float', size_hint_y=None, height=dp(40))

        selection_layout.add_widget(Label(text="Categoría:"))
        selection_layout.add_widget(self.categoria_input)
        selection_layout.add_widget(Label(text="Horas Extras:"))
        selection_layout.add_widget(self.horas_extras_input)

        buttons_layout = BoxLayout(size_hint_y=None, height=dp(50), spacing=dp(10))

        cancel_button = Button(text='Cancelar', size_hint_x=0.5, background_color=RED)
        add_button = Button(text='Agregar', size_hint_x=0.5, background_color=GREEN)
        finish_button = Button(text='Terminar', size_hint_x=0.5, background_color=BTN_COLOR)

        cancel_button.bind(on_release=self.dismiss)
        add_button.bind(on_release=self.add_personal)
        finish_button.bind(on_release=self.on_finish)

        buttons_layout.add_widget(cancel_button)
        buttons_layout.add_widget(add_button)
        buttons_layout.add_widget(finish_button)

        layout.add_widget(search_layout)
        layout.add_widget(scroll)
        layout.add_widget(selection_layout)
        layout.add_widget(self.selected_personal_scroll)
        layout.add_widget(buttons_layout)

        self.content = layout
        self.search_input.bind(text=self.on_search_text)
        self.update_selected_personal_display()

    def on_finish(self, instance):
        self.dismiss()
        if self.main_app.personal_seleccionados:
            self.main_app.personal_button.background_color = BTN_COLOR

    def on_search_text(self, instance, value):
        self.personal_list.clear_widgets()
        if not value:
            return

        filtered_personal = [p['NOMBRE_COMPLETO'] for p in self.main_app.personal_data if value.lower() in p['NOMBRE_COMPLETO'].lower()]

        for personal in filtered_personal:
            btn = Button(text=personal, size_hint_y=None, height=dp(40), background_color=GRAY)
            btn.bind(on_release=lambda btn: self.select_personal(btn.text))
            self.personal_list.add_widget(btn)

        unique_personal_names = [p['NOMBRE_COMPLETO'] for p in self.main_app.personal_data]
        if value and value not in filtered_personal and value not in unique_personal_names:
            new_btn = Button(text=f"Agregar nuevo personal: {value}", size_hint_y=None, height=dp(40), background_color=GREEN)
            new_btn.bind(on_release=lambda btn: self.select_new_personal(value))
            self.personal_list.add_widget(new_btn)

    def select_personal(self, personal_name):
        self.personal_name.text = f"Personal: {personal_name}"
        self.categoria_input.readonly = False
        self.categoria_input.text = ""
        self.selected_categoria = None
        self.is_new_personal = False
        for personal in self.main_app.personal_data:
            if personal['NOMBRE_COMPLETO'] == personal_name:
                categoria_personal = personal['CATEGORIA']
                self.categoria_input.text = categoria_personal
                self.selected_categoria = categoria_personal
                break
        self.personal_list.clear_widgets()
        self.search_input.text = personal_name

    def select_new_personal(self, personal_name):
        self.personal_name.text = f"Personal: {personal_name}"
        self.categoria_input.readonly = False
        self.categoria_input.text = ""
        self.selected_categoria = None
        self.is_new_personal = True
        self.personal_list.clear_widgets()
        self.search_input.text = personal_name

    def add_personal(self, *args):
        personal_name = self.personal_name.text.replace("Personal: ", "")
        horas_extras = self.horas_extras_input.text
        categoria_ingresada = self.categoria_input.text

        if not horas_extras:
            horas_extras = "0.0"

        if not all([personal_name, horas_extras]):
            return

        try:
            horas_extras = float(horas_extras)
            categoria = categoria_ingresada if categoria_ingresada else "N/A"
            self.add_callback(personal_name, categoria, horas_extras, self.is_new_personal)
            self.search_input.text = ''
            self.horas_extras_input.text = ''
            self.categoria_input.text = ''
            self.personal_name.text = 'Personal: '
            self.selected_categoria = None
            self.is_new_personal = False
            self.update_selected_personal_display()
        except ValueError:
            print("Por favor ingrese horas extras válidas")

    def update_selected_personal_display(self):
        self.selected_personal_grid.clear_widgets()
        grid = self.selected_personal_grid
        grid.spacing = dp(5)
        grid.padding = dp(5)

        for personal in self.main_app.personal_seleccionados:
            card = BoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(80),
                spacing=dp(5),
                padding=dp(5),
            )
            with card.canvas.before:
                Color(*ITEM_BG_COLOR)
                Rectangle(pos=card.pos, size=card.size)

            text_layout = BoxLayout(
                orientation='vertical',
                size_hint_x=0.8,
                padding=[0,0,0,0]
            )

            nombre_label = Label(
                text=f"[b]{personal.nombre_completo}[/b]",
                markup=True,
                font_size=label_font_size,
                halign='left',
                valign='middle',
                color=WHITE,
                size_hint_y=0.5,
                text_size=(None, None),
                padding=[0,0]
            )

            detalles_label = Label(
                text=f"[size={int(label_font_size)}]{personal.categoria} • HE: {personal.horas_extras}[/size]",
                markup=True,
                halign='left',
                valign='middle',
                color=WHITE,
                size_hint_y=0.5,
                text_size=(Window.width * 0.6, None),
                padding=[0,0]
            )

            text_layout.add_widget(nombre_label)
            text_layout.add_widget(detalles_label)

            delete_btn = Button(
                text='×',
                font_size=label_font_size,
                bold=True,
                size_hint_x=None,
                width=dp(40),
                background_color=RED,
                background_normal='',
                on_press=lambda btn, p=personal, card_widget=card: self.remove_personal_from_popup(p, card_widget)
            )

            card.add_widget(text_layout)
            card.add_widget(delete_btn)
            self.selected_personal_grid.add_widget(card)

    def remove_personal_from_popup(self, personal_entry, personal_layout):
        self.main_app.personal_seleccionados.remove(personal_entry)
        self.selected_personal_grid.remove_widget(personal_layout)
        self.update_selected_personal_display()


class ReporteObraApp(App):
    title = "SYA Operaciones"

    def build(self):
        self.main_screen_layout = MainScreen(self)
        Window.flip()
        return self.main_screen_layout

    def on_start(self):
        self.materiales_data = []
        self.equipos_data = []
        self.vehiculos_data = []
        self.personal_data = []
        self.new_materials_to_add = []
        self.new_equipos_to_add = []
        self.new_vehiculos_to_add = []
        self.new_personal_to_add = []
        self.get_materiales_from_server()
        self.get_equipos_from_server()
        self.get_vehiculos_from_server()
        self.get_personal_from_server()
        self.reporte_diario_submitted = False  # Flag for single submission
        self.requerimiento_materiales_submitted = False  # Flag for single submission

    def show_reporte_diario_screen(self):
        if not self.reporte_diario_submitted:
            self.reporte_diario_screen = ReporteDiarioObraScreen(self)
            Window.clearcolor = BG_COLOR
            self.root.remove_widget(self.main_screen_layout)
            self.root.add_widget(self.reporte_diario_screen)
        else:
            self.show_already_submitted_popup("Reporte Diario de Obra")

    def show_requerimiento_materiales_screen(self):
        if not self.requerimiento_materiales_submitted:
            self.requerimiento_materiales_screen = RequerimientoMaterialesScreen(self)
            Window.clearcolor = BG_COLOR
            self.root.remove_widget(self.main_screen_layout)
            self.root.add_widget(self.requerimiento_materiales_screen)
        else:
            self.show_already_submitted_popup("Requerimiento de Materiales")

    def show_already_submitted_popup(self, form_name):
        content = BoxLayout(orientation='vertical', padding=dp(20), spacing=dp(10))
        message_label = Label(text=f"Ya has enviado el formulario de {form_name} una vez. Cierra y vuelve a abrir la aplicación para enviar de nuevo.", font_size=label_font_size, color=WHITE, halign='center')
        content.add_widget(message_label)

        popup = Popup(
            title='Formulario ya enviado',
            content=content,
            size_hint=(None, None),
            size=(dp(500), dp(250)),
            auto_dismiss=True,
            separator_height=0,
            background_color = (0.1, 0.1, 0.1, 0.9)
        )
        popup.open()

    def go_back_to_main_screen(self, current_screen):
        Window.clearcolor = BG_COLOR
        self.root.remove_widget(current_screen)
        self.root.add_widget(self.main_screen_layout)


    def get_materiales_from_server(self):
        try:
            response = requests.get(f"{API_URL}/api/materiales", timeout=10)
            response.raise_for_status()
            self.materiales_data = response.json()
            print("Materiales cargados exitosamente.")
        except requests.exceptions.ConnectionError:
            print(f"Error: No se pudo conectar al servidor.")
            self.materiales_data = []
        except requests.exceptions.Timeout:
            print(f"Error: Tiempo de espera agotado al conectar al servidor.")
            self.materiales_data = []
        except requests.exceptions.RequestException as e:
            print(f"Error al obtener materiales del servidor: {e}")
            self.materiales_data = []

    def get_equipos_from_server(self):
        try:
            response = requests.get(f"{API_URL}/api/equipos", timeout=10)
            response.raise_for_status()
            self.equipos_data = response.json()
            print("Equipos cargados exitosamente.")
        except requests.exceptions.ConnectionError:
            print(f"Error: No se pudo conectar al servidor.")
            self.equipos_data = []
        except requests.exceptions.Timeout:
            print(f"Error: Tiempo de espera agotado al conectar al servidor.")
            self.equipos_data = []
        except requests.exceptions.RequestException as e:
            print(f"Error al obtener equipos del servidor: {e}")
            self.equipos_data = []

    def get_vehiculos_from_server(self):
        try:
            response = requests.get(f"{API_URL}/api/vehiculos", timeout=10)
            response.raise_for_status()
            self.vehiculos_data = response.json()
            print("Vehículos cargados exitosamente.")
        except requests.exceptions.ConnectionError:
            print(f"Error: No se pudo conectar al servidor.")
            self.vehiculos_data = []
        except requests.exceptions.Timeout:
            print(f"Error: Tiempo de espera agotado al conectar al servidor.")
            self.vehiculos_data = []
        except requests.exceptions.RequestException as e:
            print(f"Error al obtener vehículos del servidor: {e}")
            self.vehiculos_data = []

    def get_personal_from_server(self):
        try:
            response = requests.get(f"{API_URL}/api/personal", timeout=10)
            response.raise_for_status()
            personal_data = response.json()
            for person in personal_data:
                person['NOMBRE_COMPLETO'] = person['AP. PATERNO'] + ' ' + person.get('AP. MATERNO', '') + ', ' + person['NOMBRES']
            self.personal_data = personal_data
            print("Personal de campo cargado exitosamente.")
        except requests.exceptions.ConnectionError:
            print(f"Error: No se pudo conectar al servidor.")
            self.personal_data = []
        except requests.exceptions.Timeout:
            print(f"Error: Tiempo de espera agotado al conectar al servidor.")
            self.personal_data = []
        except requests.exceptions.RequestException as e:
            print(f"Error al obtener personal del servidor: {e}")
            self.personal_data = []


class RequerimientoMaterialesScreen(BoxLayout):
    def __init__(self, main_app, **kwargs):
        super().__init__(**kwargs)
        self.main_app = main_app
        self.orientation = 'vertical'
        self.padding = dp(SPACING_LG)
        self.spacing = dp(SPACING_MD)

        self.requerimientos_seleccionados = []
        self.materiales_data = main_app.materiales_data
        self.equipos_data = main_app.equipos_data

        # Initialize popups
        self.material_popup = MaterialSelectionPopupRequerimiento(
            add_callback=self.add_requerimiento_material,
            main_app=self
        )
        self.equipo_popup = EquipoSelectionPopupRequerimiento(
            add_callback=self.add_requerimiento_equipo,
            main_app=self
        )

        # Create modern form layout
        self.create_header()
        self.create_form_section()
        self.create_actions_section()
        self.create_selected_items_section()
        self.create_submit_section()

        self.update_selected_items_display()

    def create_header(self):
        """Create modern header section"""
        header_container = BoxLayout(
            orientation='vertical',
            size_hint_y=None,
            height=dp(100),
            spacing=dp(SPACING_SM)
        )

        title = create_label(
            'Requerimiento de Materiales',
            style='h1',
            color=PRIMARY_COLOR
        )
        title.bold = True

        subtitle = create_label(
            'Solicitar materiales y equipos para las operaciones',
            style='caption',
            color=TEXT_SECONDARY
        )

        header_container.add_widget(title)
        header_container.add_widget(subtitle)
        self.add_widget(header_container)

    def create_form_section(self):
        """Create form input section"""
        form_container = BoxLayout(
            orientation='vertical',
            size_hint_y=None,
            height=dp(240),
            spacing=dp(SPACING_MD)
        )

        # Form title
        form_title = create_label('Información General', style='h3')
        form_title.halign = 'left'
        form_container.add_widget(form_title)

        # Date field
        date_container = BoxLayout(orientation='vertical', spacing=dp(SPACING_XS), size_hint_y=None, height=dp(60))
        date_container.add_widget(create_label('Fecha', style='body'))
        self.fecha_input = create_text_input(hint_text='DD/MM/YYYY')
        self.fecha_input.text = datetime.now().strftime('%d/%m/%Y')
        date_container.add_widget(self.fecha_input)
        form_container.add_widget(date_container)

        # Code field
        code_container = BoxLayout(orientation='vertical', spacing=dp(SPACING_XS), size_hint_y=None, height=dp(60))
        code_container.add_widget(create_label('Código de Obra', style='body'))
        self.codigo_obra_input = create_text_input(hint_text='Ingrese el código de obra')
        code_container.add_widget(self.codigo_obra_input)
        form_container.add_widget(code_container)

        # Engineer field
        engineer_container = BoxLayout(orientation='vertical', spacing=dp(SPACING_XS), size_hint_y=None, height=dp(60))
        engineer_container.add_widget(create_label('Nombre del Ingeniero', style='body'))
        self.nombre_ingeniero_input = create_text_input(hint_text='Ingrese el nombre del ingeniero')
        engineer_container.add_widget(self.nombre_ingeniero_input)
        form_container.add_widget(engineer_container)

        self.add_widget(form_container)

    def create_actions_section(self):
        """Create action buttons section"""
        actions_container = BoxLayout(
            orientation='vertical',
            size_hint_y=None,
            height=dp(120),
            spacing=dp(SPACING_MD)
        )

        actions_title = create_label('Agregar Elementos', style='h3')
        actions_title.halign = 'left'
        actions_container.add_widget(actions_title)

        buttons_layout = BoxLayout(
            orientation='horizontal',
            spacing=dp(SPACING_MD),
            size_hint_y=None,
            height=dp(BUTTON_HEIGHT)
        )

        self.materiales_button = create_button(
            '📦 Agregar Material',
            style='secondary',
            on_press=self.show_material_popup
        )

        self.equipos_button = create_button(
            '🔧 Agregar Equipo',
            style='secondary',
            on_press=self.show_equipo_popup
        )

        buttons_layout.add_widget(self.materiales_button)
        buttons_layout.add_widget(self.equipos_button)
        actions_container.add_widget(buttons_layout)

        self.add_widget(actions_container)

    def create_selected_items_section(self):
        """Create selected items display section"""
        selected_container = BoxLayout(
            orientation='vertical',
            size_hint_y=0.4,
            spacing=dp(SPACING_SM)
        )

        selected_title = create_label('Elementos Seleccionados', style='h3')
        selected_title.halign = 'left'
        selected_container.add_widget(selected_title)

        self.selected_items_scroll = ScrollView()
        self.selected_items_grid = GridLayout(
            cols=1,
            spacing=dp(SPACING_SM),
            size_hint_y=None,
            padding=dp(SPACING_SM)
        )
        self.selected_items_grid.bind(minimum_height=self.selected_items_grid.setter('height'))
        self.selected_items_scroll.add_widget(self.selected_items_grid)

        selected_container.add_widget(self.selected_items_scroll)
        self.add_widget(selected_container)

    def create_submit_section(self):
        """Create submit button section"""
        submit_container = BoxLayout(
            orientation='vertical',
            size_hint_y=None,
            height=dp(120),
            spacing=dp(SPACING_MD)
        )

        # Buttons layout
        buttons_layout = BoxLayout(
            orientation='horizontal',
            spacing=dp(SPACING_MD),
            size_hint_y=None,
            height=dp(BUTTON_HEIGHT_LG)
        )

        # Back button
        back_button = create_button(
            '← VOLVER',
            style='surface',
            size=(dp(150), dp(BUTTON_HEIGHT_LG)),
            on_press=self.go_back_to_main
        )

        # Submit button
        self.enviar_button = create_button(
            'ENVIAR REQUERIMIENTOS',
            style='primary',
            size=(dp(300), dp(BUTTON_HEIGHT_LG)),
            on_press=self.enviar_requerimientos
        )
        self.enviar_button.disabled = self.main_app.requerimiento_materiales_submitted

        buttons_layout.add_widget(back_button)
        buttons_layout.add_widget(Widget())  # Spacer
        buttons_layout.add_widget(self.enviar_button)

        submit_container.add_widget(Widget())  # Spacer
        submit_container.add_widget(buttons_layout)
        self.add_widget(submit_container)

    def go_back_to_main(self, instance):
        """Navigate back to main screen"""
        self.main_app.go_back_to_main_screen(self)

    def show_material_popup(self, instance):
        self.material_popup.open()

    def show_equipo_popup(self, instance):
        self.equipo_popup.open()

    def add_requerimiento_material(self, material_name, unit, quantity, is_new_material=False):
        try:
            quantity = float(quantity)
        except ValueError:
            print("Cantidad inválida. Por favor, ingrese un número.")
            return

        requerimiento_entry = MaterialEntry(material_name, unit, float(quantity))
        self.requerimientos_seleccionados.append(requerimiento_entry)
        self.update_selected_items_display()

    def add_requerimiento_equipo(self, equipo_name, cantidad, unidad, is_new_equipo=False):  # unidad instead of propiedad
        requerimiento_entry = RequerimientoEquipoEntry(equipo_name, unidad, cantidad)  # Use RequerimientoEquipoEntry
        self.requerimientos_seleccionados.append(requerimiento_entry)
        self.update_selected_items_display()

    def update_selected_items_display(self):
        self.selected_items_grid.clear_widgets()

        if not self.requerimientos_seleccionados:
            # Show empty state
            empty_label = create_label(
                "No hay elementos seleccionados",
                style='caption',
                color=TEXT_MUTED
            )
            empty_label.halign = 'center'
            self.selected_items_grid.add_widget(empty_label)
            return

        for item in self.requerimientos_seleccionados:
            # Create modern item card
            card = BoxLayout(
                orientation='horizontal',
                size_hint_y=None,
                height=dp(80),
                spacing=dp(SPACING_MD),
                padding=dp(SPACING_MD)
            )

            # Add modern card background
            with card.canvas.before:
                Color(*SURFACE_LIGHT)
                card.bg_rect = Rectangle(pos=card.pos, size=card.size)

            def update_card_bg(instance, value):
                card.bg_rect.pos = instance.pos
                card.bg_rect.size = instance.size

            card.bind(pos=update_card_bg, size=update_card_bg)

            # Icon based on item type
            icon_container = BoxLayout(
                orientation='vertical',
                size_hint_x=None,
                width=dp(50),
                padding=(0, dp(SPACING_SM))
            )

            if isinstance(item, MaterialEntry):
                icon_text = '📦'
                type_color = SECONDARY_COLOR
                title_text = f"Material: {item.nombre}"
            elif isinstance(item, RequerimientoEquipoEntry):
                icon_text = '🔧'
                type_color = PRIMARY_COLOR
                title_text = f"Equipo: {item.nombre}"
            else:
                icon_text = '📋'
                type_color = TEXT_SECONDARY
                title_text = item.nombre

            icon_label = Label(
                text=icon_text,
                font_size=dp(24),
                size_hint_y=None,
                height=dp(30),
                color=type_color
            )
            icon_container.add_widget(icon_label)

            # Content section
            content_layout = BoxLayout(
                orientation='vertical',
                spacing=dp(SPACING_XS),
                padding=(0, dp(SPACING_XS))
            )

            # Item name with type prefix
            nombre_label = create_label(
                title_text,
                style='body',
                color=TEXT_PRIMARY
            )
            nombre_label.bold = True
            nombre_label.halign = 'left'
            nombre_label.text_size = (dp(300), None)
            nombre_label.size_hint_y = 0.6

            # Quantity and unit
            detalles_label = create_label(
                f"{item.cantidad} {item.unidad}",
                style='caption',
                color=TEXT_SECONDARY
            )
            detalles_label.halign = 'left'
            detalles_label.text_size = (dp(300), None)
            detalles_label.size_hint_y = 0.4

            content_layout.add_widget(nombre_label)
            content_layout.add_widget(detalles_label)

            # Delete button with modern styling
            delete_btn = create_button(
                '×',
                style='danger',
                size=(dp(40), dp(40)),
                on_press=lambda btn, req_item=item, card_widget=card: self.remove_requerimiento_item(req_item, card_widget)
            )
            delete_btn.size_hint = (None, None)
            delete_btn.font_size = dp(20)
            delete_btn.bold = True

            # Assemble card
            card.add_widget(icon_container)
            card.add_widget(content_layout)
            card.add_widget(delete_btn)
            self.selected_items_grid.add_widget(card)

    def remove_requerimiento_item(self, item_entry, item_layout):
        self.requerimientos_seleccionados.remove(item_entry)
        self.selected_items_grid.remove_widget(item_layout)
        self.update_selected_items_display()

    def enviar_requerimientos(self, instance):
        if not self.validar_requerimientos():
            return

        datos = {
            "fecha": self.fecha_input.text,  # Added fecha
            "codigo_obra": self.codigo_obra_input.text,
            "nombre_ingeniero": self.nombre_ingeniero_input.text,
            "requerimientos": [item.to_dict() for item in self.requerimientos_seleccionados]  # to_dict() will work for both MaterialEntry and RequerimientoEquipoEntry
        }

        print("Datos a enviar requerimientos:", datos)  # Log datos before sending

        try:
            response = requests.post(f"{API_URL}/recibir-requerimientos", json=datos, timeout=20)  # Corrected URL
            response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
            if response.status_code == 200:
                print("Requerimientos enviados exitosamente")
                self.requerimientos_seleccionados = []
                self.update_selected_items_display()
                self.show_success_popup()
                self.enviar_button.disabled = True  # Disable button after successful submission
                self.main_app.requerimiento_materiales_submitted = True  # Set flag in main app
            else:
                print(f"Error al enviar requerimientos: {response.status_code} - {response.text}")
                print(f"Error detail: {response.text}")  # Print the error detail
        except requests.exceptions.ConnectionError:
            print(f"Error: No se pudo conectar al servidor para enviar los requerimientos.")
        except requests.exceptions.Timeout:
            print(f"Error: Tiempo de espera agotado al enviar los requerimientos al servidor.")
        except requests.exceptions.RequestException as e:
            print(f"Error al enviar los requerimientos: {e}")
            print(f"Detailed error: {e.response.text if e.response else e}")  # Print more details if available

    def validar_requerimientos(self):
        if not self.fecha_input.text:  # Added fecha validation
            print("El campo 'Fecha' es obligatorio.")
            return False
        try:
            datetime.strptime(self.fecha_input.text, '%d/%m/%Y')
        except ValueError:
            print("Formato de fecha inválido. Use DD/MM/YYYY.")
            return False
        if not self.codigo_obra_input.text:
            print("El campo 'Código de Obra' es obligatorio.")
            return False
        if not self.nombre_ingeniero_input.text:
            print("El campo 'Nombre del Ingeniero' es obligatorio.")
            return False
        if not self.requerimientos_seleccionados:
            print("Debe seleccionar al menos un material o equipo.")
            return False
        return True

    def show_success_popup(self):
        content = BoxLayout(orientation='vertical', padding=dp(20), spacing=dp(10))
        message_label = Label(text="Los requerimientos se han enviado correctamente", font_size=label_font_size, color=WHITE)
        content.add_widget(message_label)

        popup = Popup(
            title='',
            content=content,
            size_hint=(None, None),
            size=(dp(400), dp(200)),
            auto_dismiss=True,
            separator_height=0,
            background_color = (0.1, 0.1, 0.1, 0.9)
        )
        popup.open()
        anim = Animation(opacity=0, duration=3.0) + Animation(size=(0, 0), duration=3.0)
        anim.bind(on_complete=lambda *args: popup.dismiss())
        anim.start(popup)


class MaterialSelectionPopupRequerimiento(Popup):
    def __init__(self, add_callback, main_app, **kwargs):
        self.add_callback = add_callback
        self.main_app = main_app
        kwargs.pop('add_callback', None)
        kwargs.pop('main_app', None)
        super().__init__(**kwargs)
        self.title = "Selección de Materiales"
        self.size_hint = (0.9, 0.9)
        self.is_new_material = False

        layout = BoxLayout(orientation='vertical', spacing=dp(10), padding=dp(10))

        search_layout = BoxLayout(size_hint_y=None, height=dp(40))
        self.search_input = TextInput(
            hint_text='Buscar material...',
            multiline=False,
            size_hint=(1, None),
            height=dp(40)
        )
        search_layout.add_widget(self.search_input)

        scroll = ScrollView(size_hint_y=0.5)
        self.materials_list = GridLayout(
            cols=1,
            spacing=dp(5),
            size_hint_y=None,
            padding=dp(5)
        )
        self.materials_list.bind(minimum_height=self.materials_list.setter('height'))
        scroll.add_widget(self.materials_list)

        selection_layout = GridLayout(cols=2, spacing=dp(10), size_hint_y=0.5, height=dp(120), padding=dp(10))

        self.material_name = Label(text="Material: ")
        self.unit_input = TextInput(hint_text='Unidad', multiline=False, size_hint_y=None, height=dp(40))
        self.quantity_input = TextInput(hint_text='Cantidad', multiline=False, input_filter='float', size_hint_y=None,
                                        height=dp(40))

        selection_layout.add_widget(Label(text="Unidad:"))
        selection_layout.add_widget(self.unit_input)
        selection_layout.add_widget(Label(text="Cantidad:"))
        selection_layout.add_widget(self.quantity_input)

        buttons_layout = BoxLayout(size_hint_y=None, height=dp(50), spacing=dp(10))

        cancel_button = Button(text='Cancelar', size_hint_x=0.5, background_color=RED)
        add_button = Button(text='Agregar', size_hint_x=0.5, background_color=GREEN)

        cancel_button.bind(on_release=self.dismiss)
        add_button.bind(on_release=self.add_material)

        buttons_layout.add_widget(cancel_button)
        buttons_layout.add_widget(add_button)

        layout.add_widget(search_layout)
        layout.add_widget(scroll)
        layout.add_widget(selection_layout)
        layout.add_widget(buttons_layout)

        self.content = layout
        self.search_input.bind(text=self.on_search_text)

    def on_search_text(self, instance, value):
        self.materials_list.clear_widgets()
        if not value:
            return

        filtered_materials = [m['nombre_material'] for m in self.main_app.materiales_data if
                              value.lower() in m['nombre_material'].lower()]

        for material in filtered_materials:
            btn = Button(text=material, size_hint_y=None, height=dp(40), background_color=GRAY)
            btn.bind(on_release=lambda btn: self.select_material(btn.text))
            self.materials_list.add_widget(btn)

        unique_material_names = [m['nombre_material'] for m in self.main_app.materiales_data]
        if value and value not in filtered_materials and value not in unique_material_names:
            new_btn = Button(text=f"Agregar nuevo material: {value}", size_hint_y=None, height=dp(40),
                             background_color=GREEN)
            new_btn.bind(on_release=lambda btn: self.select_new_material(value))
            self.materials_list.add_widget(new_btn)

    def select_material(self, material_name):
        self.material_name.text = f"Material: {material_name}"
        self.unit_input.readonly = False
        self.unit_input.text = ""
        self.is_new_material = False
        for material in self.main_app.materiales_data:
            if material['nombre_material'] == material_name:
                unit = material['unidad']
                self.unit_input.text = unit
                break
        self.materials_list.clear_widgets()
        self.search_input.text = material_name

    def select_new_material(self, material_name):
        self.material_name.text = f"Material: {material_name}"
        self.unit_input.readonly = False
        self.unit_input.text = ""
        self.is_new_material = True
        self.materials_list.clear_widgets()
        self.search_input.text = material_name

    def add_material(self, *args):
        material_name = self.material_name.text.replace("Material: ", "")
        unit = self.unit_input.text
        quantity = self.quantity_input.text

        if not all([material_name, unit, quantity]):
            return

        try:
            quantity = float(quantity)
            self.add_callback(material_name, unit, quantity, self.is_new_material)
            self.search_input.text = ''
            self.unit_input.text = ''
            self.quantity_input.text = ''
            self.material_name.text = 'Material: '
            self.is_new_material = False
            self.dismiss()
        except ValueError:
            print("Por favor ingrese una cantidad válida")


class EquipoSelectionPopupRequerimiento(Popup):  # Popup for Requerimiento Materiales - Modified
    def __init__(self, add_callback, main_app, **kwargs):
        self.add_callback = add_callback
        self.main_app = main_app
        kwargs.pop('add_callback', None)
        kwargs.pop('main_app', None)
        super().__init__(**kwargs)
        self.title = "Selección de Equipos"
        self.size_hint = (0.9, 0.9)
        self.is_new_equipo = False

        layout = BoxLayout(orientation='vertical', spacing=dp(10), padding=dp(10))

        search_layout = BoxLayout(size_hint_y=None, height=dp(40))
        self.search_input = TextInput(
            hint_text='Buscar equipo...',
            multiline=False,
            size_hint=(1, None),
            height=dp(40)
        )
        search_layout.add_widget(self.search_input)

        scroll = ScrollView(size_hint_y=0.5)
        self.equipos_list = GridLayout(cols=1, spacing=dp(5), size_hint_y=None, padding=dp(5))
        self.equipos_list.bind(minimum_height=self.equipos_list.setter('height'))
        scroll.add_widget(self.equipos_list)

        selection_layout = GridLayout(cols=2, spacing=dp(10), size_hint_y=0.5, height=dp(120), padding=dp(10))

        self.equipo_name = Label(text="Equipo: ")
        self.unidad_input = TextInput(hint_text='Unidad', multiline=False, size_hint_y=None,
                                      height=dp(40))  # Changed to unidad_input - Now Unidad is used for Requerimiento
        self.quantity_input = TextInput(hint_text='Cantidad', multiline=False, input_filter='float', size_hint_y=None,
                                        height=dp(40))

        selection_layout.add_widget(Label(
            text="Unidad:"))  # Changed to Unidad Label - Now Unidad is used for Requerimiento
        selection_layout.add_widget(
            self.unidad_input)  # Changed to unidad_input
        selection_layout.add_widget(Label(text="Cantidad:"))
        selection_layout.add_widget(self.quantity_input)

        buttons_layout = BoxLayout(size_hint_y=None, height=dp(50), spacing=dp(10))

        cancel_button = Button(text='Cancelar', size_hint_x=0.5, background_color=RED)
        add_button = Button(text='Agregar', size_hint_x=0.5, background_color=GREEN)  # Corrected: Set background_color here

        cancel_button.bind(on_release=self.dismiss)
        add_button.bind(on_release=self.add_equipo_requerimiento)  # Changed to add_equipo_requerimiento

        buttons_layout.add_widget(cancel_button)
        buttons_layout.add_widget(add_button)

        layout.add_widget(search_layout)
        layout.add_widget(scroll)
        layout.add_widget(selection_layout)
        layout.add_widget(buttons_layout)

        self.content = layout
        self.search_input.bind(text=self.on_search_text)

    def on_search_text(self, instance, value):  # For Requerimiento Materiales - No changes needed
        self.equipos_list.clear_widgets()
        if not value:
            return

        filtered_equipos = [e['nombre_equipo'] for e in self.main_app.equipos_data if
                            value.lower() in e['nombre_equipo'].lower()]

        for equipo in filtered_equipos:
            btn = Button(text=equipo, size_hint_y=None, height=dp(40), background_color=GRAY)
            btn.bind(on_release=lambda btn: self.select_equipo(btn.text))
            self.equipos_list.add_widget(btn)

        unique_equipo_names = [e['nombre_equipo'] for e in self.main_app.equipos_data]
        if value and value not in filtered_equipos and value not in unique_equipo_names:
            new_btn = Button(text=f"Agregar nuevo equipo: {value}", size_hint_y=None, height=dp(40),
                             background_color=GREEN)
            new_btn.bind(on_release=lambda btn: self.select_new_equipo(value))
            self.equipos_list.add_widget(new_btn)

    def select_equipo(self, equipo_name):  # For Requerimiento Materiales - No changes needed
        self.equipo_name.text = f"Equipo: {equipo_name}"
        self.unidad_input.readonly = False  # Changed to unidad_input
        self.unidad_input.text = ""  # Changed to unidad_input
        self.is_new_equipo = False
        for equipo in self.main_app.equipos_data:
            if equipo['nombre_equipo'] == equipo_name:
                unidad = "und"  # Default unidad for equipos in requerimientos
                self.unidad_input.text = unidad  # Changed to unidad_input
                break
        self.equipos_list.clear_widgets()
        self.search_input.text = equipo_name

    def select_new_equipo(self, equipo_name):  # For Requerimiento Materiales - No changes needed
        self.equipo_name.text = f"Equipo: {equipo_name}"
        self.unidad_input.readonly = False  # Changed to unidad_input
        self.unidad_input.text = ""  # Changed to unidad_input
        self.is_new_equipo = True
        self.equipos_list.clear_widgets()
        self.search_input.text = equipo_name

    def add_equipo_requerimiento(self, *args):  # Changed function name and logic for Requerimiento
        equipo_name = self.equipo_name.text.replace("Equipo: ", "")
        unidad = self.unidad_input.text  # Now unidad is taken from unidad_input
        cantidad = self.quantity_input.text

        if not all([equipo_name, unidad, cantidad]):  # Now check for unidad as well
            return

        try:
            cantidad = float(cantidad)
            self.add_callback(equipo_name, cantidad, unidad, self.is_new_equipo)  # Pass unidad to add_callback
            self.search_input.text = ''
            self.unidad_input.text = ''  # Clear unidad_input
            self.quantity_input.text = ''
            self.equipo_name.text = 'Equipo: '
            self.is_new_equipo = False
            self.dismiss()
        except ValueError:
            print("Por favor ingrese una cantidad válida")


class ReporteDiarioObraScreen(BoxLayout):
    def __init__(self, main_app, **kwargs):
        super().__init__(**kwargs)
        self.main_app = main_app
        self.orientation = 'vertical'

        self.respuestas = {}
        self.materiales_seleccionados = []
        self.equipos_seleccionados = []
        self.vehiculos_seleccionados = []
        self.personal_seleccionados = []
        self.materiales_data = main_app.materiales_data
        self.equipos_data = main_app.equipos_data
        self.vehiculos_data = main_app.vehiculos_data
        self.personal_data = main_app.personal_data
        self.new_materials_to_add = main_app.new_materials_to_add  # List to hold new materials to be added to CSV
        self.new_equipos_to_add = main_app.new_equipos_to_add  # List to hold new equipos to be added to CSV
        self.new_vehiculos_to_add = main_app.new_vehiculos_to_add  # List to hold new vehiculos to be added to CSV
        self.new_personal_to_add = main_app.new_personal_to_add  # List to hold new personal to be added to CSV
        self.submit_button = None  # Initialize submit_button as class attribute

        self.campos = [
            {"nombre": "fecha", "tipo": "date", "etiqueta": "Fecha"},
            {"nombre": "codigo_obra", "tipo": "text", "etiqueta": "Código de Obra"},
            {"nombre": "nombre_ingeniero", "tipo": "text", "etiqueta": "Nombre del Ingeniero"},
            {"nombre": "nombre_supervisor", "tipo": "text", "etiqueta": "Nombre del Supervisor Seguridad"},
            {"nombre": "actividad_principal", "tipo": "text", "etiqueta": "Actividad Realizada"},
            {"nombre": "materiales_usados", "tipo": "material", "etiqueta": "Materiales Usados"},
            {"nombre": "equipos_usados", "tipo": "equipo", "etiqueta": "Equipos Usados"},
            {"nombre": "vehiculos_usados", "tipo": "vehiculo", "etiqueta": "Vehículos Usados"},
            {"nombre": "personal_de_campo", "tipo": "personal", "etiqueta": "Personal de Campo"},
            {"nombre": "supervisor_presente", "tipo": "bool", "etiqueta": "¿Supervisor Presente?"},
            {"nombre": "avance_diario", "tipo": "text", "etiqueta": "Avance Diario"},
            {"nombre": "incidentes", "tipo": "text", "etiqueta": "Incidentes ocurridos"},
            {"nombre": "siguiente_dia", "tipo": "text", "etiqueta": "Plan para Siguiente Día"},
            {"nombre": "observaciones", "tipo": "text", "etiqueta": "Observaciones"}
        ]

        form_layout = BoxLayout(orientation='vertical', spacing=dp(margin), padding=(dp(margin), dp(margin)),
                                size_hint_y=None)
        form_layout.bind(minimum_height=form_layout.setter("height"))

        for campo in self.campos:
            if campo["nombre"] in ["materiales_usados", "equipos_usados", "vehiculos_usados", "personal_de_campo"]:
                form_layout.add_widget(create_label(campo["etiqueta"], titulo=True))
                if campo["nombre"] == "materiales_usados":
                    self.setup_material_input(form_layout)
                elif campo["nombre"] == "equipos_usados":
                    self.setup_equipo_input(form_layout)
                elif campo["nombre"] == "vehiculos_usados":
                    self.setup_vehiculo_input(form_layout)
                elif campo["nombre"] == "personal_de_campo":
                    self.setup_personal_input(form_layout)
                continue

            form_layout.add_widget(create_label(campo["etiqueta"], titulo=True))

            if campo["tipo"] == "bool":
                input_widget = Spinner(
                    text='Sí',
                    values=('Sí', 'No'),
                    size_hint=(1, None),
                    height=dp(input_height),
                    pos_hint={'center_x': 0.5},
                    background_color=GRAY,
                    color=WHITE,
                    font_size=label_font_size
                )
            else:
                input_widget = create_text_input()
                if campo["nombre"] == "fecha":
                    input_widget.text = datetime.now().strftime('%d/%m/%Y')

            self.respuestas[campo["nombre"]] = input_widget
            form_layout.add_widget(input_widget)

        # Create buttons layout
        buttons_layout = BoxLayout(
            orientation='horizontal',
            spacing=dp(SPACING_MD),
            size_hint_y=None,
            height=dp(BUTTON_HEIGHT_LG)
        )

        # Back button
        back_button = create_button(
            '← VOLVER',
            style='surface',
            size=(dp(150), dp(BUTTON_HEIGHT_LG)),
            on_press=self.go_back_to_main
        )

        # Submit button
        self.submit_button = create_button(
            'ENVIAR DATOS',
            style='primary',
            size=(dp(300), dp(BUTTON_HEIGHT_LG)),
            on_press=self.confirmar_envio
        )
        self.submit_button.disabled = main_app.reporte_diario_submitted  # Disable button if already submitted

        buttons_layout.add_widget(back_button)
        buttons_layout.add_widget(Widget())  # Spacer
        buttons_layout.add_widget(self.submit_button)

        form_layout.add_widget(Widget(size_hint_y=None, height=dp(margin * 0.1)))
        form_layout.add_widget(buttons_layout)

        self.material_popup = MaterialSelectionPopup(add_callback=self.add_material_with_quantity, main_app=self)
        self.equipo_popup = EquipoSelectionPopup(add_callback=self.add_equipo_with_quantity, main_app=self)
        self.vehiculo_popup = VehiculoSelectionPopup(add_callback=self.add_vehiculo_with_placa_propiedad, main_app=self)
        self.personal_popup = PersonalSelectionPopup(add_callback=self.add_personal_with_horas_extras, main_app=self)

        scroll = ScrollView(size_hint=(1, 1), do_scroll_x=False)
        scroll.add_widget(form_layout)

        self.add_widget(scroll)

    def setup_material_input(self, form_layout):
        material_layout = BoxLayout(orientation='vertical', spacing=dp(margin), size_hint_y=None)
        material_layout.bind(minimum_height=material_layout.setter("height"))
        self.materiales_button = create_button('Seleccionar Materiales', size=(400, button_height), color=GREEN,
                                               on_press=self.show_material_popup)
        material_layout.add_widget(self.materiales_button)
        form_layout.add_widget(material_layout)

    def setup_equipo_input(self, form_layout):
        equipo_layout = BoxLayout(orientation='vertical', spacing=dp(margin), size_hint_y=None)
        equipo_layout.bind(minimum_height=equipo_layout.setter("height"))
        self.equipos_button = create_button('Seleccionar Equipos', size=(400, button_height), color=GREEN,
                                            on_press=self.show_equipo_popup)
        equipo_layout.add_widget(self.equipos_button)
        form_layout.add_widget(equipo_layout)

    def setup_vehiculo_input(self, form_layout):
        vehiculo_layout = BoxLayout(orientation='vertical', spacing=dp(margin), size_hint_y=None)
        vehiculo_layout.bind(minimum_height=vehiculo_layout.setter("height"))
        self.vehiculos_button = create_button('Seleccionar Vehículos', size=(400, button_height), color=GREEN,
                                              on_press=self.show_vehiculo_popup)
        vehiculo_layout.add_widget(self.vehiculos_button)
        form_layout.add_widget(vehiculo_layout)

    def setup_personal_input(self, form_layout):
        personal_layout = BoxLayout(orientation='vertical', spacing=dp(margin), size_hint_y=None)
        personal_layout.bind(minimum_height=personal_layout.setter("height"))
        self.personal_button = create_button('Seleccionar Personal', size=(400, button_height), color=GREEN,
                                             on_press=self.show_personal_popup)
        personal_layout.add_widget(self.personal_button)
        form_layout.add_widget(personal_layout)

    def go_back_to_main(self, instance):
        """Navigate back to main screen"""
        self.main_app.go_back_to_main_screen(self)

    def show_material_popup(self, instance):
        if self.materiales_seleccionados:
            self.materiales_button.background_color = BTN_COLOR
        else:
            self.materiales_button.background_color = GREEN
        self.material_popup.open()

    def show_equipo_popup(self, instance):
        if self.equipos_seleccionados:
            self.equipos_button.background_color = BTN_COLOR
        else:
            self.equipos_button.background_color = GREEN
        self.equipo_popup.open()

    def show_vehiculo_popup(self, instance):
        if self.vehiculos_seleccionados:
            self.vehiculos_button.background_color = BTN_COLOR
        else:
            self.vehiculos_button.background_color = GREEN
        self.vehiculo_popup.open()

    def show_personal_popup(self, instance):
        if self.personal_seleccionados:
            self.personal_button.background_color = BTN_COLOR
        else:
            self.personal_button.background_color = GREEN
        self.personal_popup.open()

    def add_material_with_quantity(self, material_name, unit, quantity, is_new_material=False):
        try:
            float(quantity)
        except ValueError:
            print("Cantidad inválida. Por favor, ingrese un número.")
            return

        material_entry = MaterialEntry(material_name, unit, float(quantity))
        self.materiales_seleccionados.append(material_entry)
        if is_new_material:
            self.new_materials_to_add.append(
                {'nombre_material': material_name, 'unidad': unit})  # Add to new items list
        self.material_popup.update_selected_materials_display()
        self.materiales_button.background_color = BTN_COLOR

    def add_equipo_with_quantity(self, equipo_name, cantidad, propiedad, is_new_equipo=False):
        equipo_entry = EquipoEntry(equipo_name, cantidad, propiedad)
        self.equipos_seleccionados.append(equipo_entry)
        if is_new_equipo:
            self.new_equipos_to_add.append({'nombre_equipo': equipo_name, 'propiedad': propiedad})
        self.equipo_popup.update_selected_equipos_display()
        self.equipos_button.background_color = BTN_COLOR

    def add_vehiculo_with_placa_propiedad(self, vehiculo_name, placa, propiedad, is_new_vehiculo=False):
        vehiculo_entry = VehiculoEntry(vehiculo_name, placa, propiedad)
        self.vehiculos_seleccionados.append(vehiculo_entry)
        if is_new_vehiculo:
            self.new_vehiculos_to_add.append({'nombre_vehiculo': vehiculo_name, 'placa': placa, 'propiedad': propiedad})
        self.vehiculo_popup.update_selected_vehiculos_display()
        self.vehiculos_button.background_color = BTN_COLOR

    def add_personal_with_horas_extras(self, nombre_completo, categoria, horas_extras, is_new_personal=False):
        personal_entry = PersonalEntry(nombre_completo, categoria, horas_extras)
        self.personal_seleccionados.append(personal_entry)
        if is_new_personal:
            self.new_personal_to_add.append({'nombre_completo': nombre_completo, 'categoria': categoria})
        self.personal_popup.update_selected_personal_display()
        self.personal_button.background_color = BTN_COLOR

    def remove_material(self, material_entry, material_layout):
        self.materiales_seleccionados.remove(material_entry)
        self.material_popup.update_selected_materials_display()
        if not self.materiales_seleccionados:
            self.materiales_button.background_color = GREEN

    def remove_equipo(self, equipo_entry, equipo_layout):
        self.equipos_seleccionados.remove(equipo_entry)
        self.equipo_popup.update_selected_equipos_display()
        if not self.equipos_seleccionados:
            self.equipos_button.background_color = GREEN

    def remove_vehiculo(self, vehiculo_entry, vehiculo_layout):
        self.vehiculos_seleccionados.remove(vehiculo_entry)
        self.vehiculo_popup.update_selected_vehiculos_display()
        if not self.vehiculos_seleccionados:
            self.vehiculos_button.background_color = GREEN

    def remove_personal(self, personal_entry, personal_layout):
        self.personal_seleccionados.remove(personal_entry)
        self.personal_popup.update_selected_personal_display()
        if not self.personal_seleccionados:
            self.personal_button.background_color = GREEN

    def show_success_popup(self):
        content = BoxLayout(orientation='vertical', padding=dp(20), spacing=dp(10))
        message_label = Label(text="Los datos se han enviado correctamente", font_size=label_font_size, color=WHITE)
        content.add_widget(message_label)

        popup = Popup(
            title='',
            content=content,
            size_hint=(None, None),
            size=(dp(400), dp(200)),
            auto_dismiss=True,
            separator_height=0,
            background_color=(0.1, 0.1, 0.1, 0.9)
        )
        popup.open()
        anim = Animation(opacity=0, duration=3.0) + Animation(size=(0, 0), duration=3.0)
        anim.bind(on_complete=lambda *args: popup.dismiss())
        anim.start(popup)

    def confirmar_envio(self, instance):
        if not self.validar_datos():
            return

        # Add new items to CSVs first
        new_items_added_successfully = True
        for material_data in self.new_materials_to_add:
            response = requests.post(f"{API_URL}/api/materiales/new", json=material_data, timeout=10)
            if response.status_code != 201:
                print(f"Error adding new material: {response.status_code} - {response.text}")
                new_items_added_successfully = False
                break
        if not new_items_added_successfully: return False

        for equipo_data in self.new_equipos_to_add:
            response = requests.post(f"{API_URL}/api/equipos/new", json=equipo_data, timeout=10)
            if response.status_code != 201:
                print(f"Error adding new equipo: {response.status_code} - {response.text}")
                new_items_added_successfully = False
                break
        if not new_items_added_successfully: return False

        for vehiculo_data in self.new_vehiculos_to_add:
            response = requests.post(f"{API_URL}/api/vehiculos/new", json=vehiculo_data, timeout=10)
            if response.status_code != 201:
                print(f"Error adding new vehiculo: {response.status_code} - {response.text}")
                new_items_added_successfully = False
                break
        if not new_items_added_successfully: return False

        for personal_data in self.new_personal_to_add:
            response = requests.post(f"{API_URL}/api/personal/new", json=personal_data, timeout=10)
            if response.status_code != 201:
                print(f"Error adding new personal: {response.status_code} - {response.text}")
                new_items_added_successfully = False
                break
        if not new_items_added_successfully: return False

        datos = {
            campo["nombre"]: (
                [material.to_dict() for material in self.materiales_seleccionados] if campo["nombre"] == "materiales_usados" else
                [equipo.to_dict() for equipo in self.equipos_seleccionados] if campo["nombre"] == "equipos_usados" else
                [vehiculo.to_dict() for vehiculo in self.vehiculos_seleccionados] if campo["nombre"] == "vehiculos_usados" else
                [personal.to_dict() for personal in self.personal_seleccionados] if campo["nombre"] == "personal_de_campo" else
                (self.respuestas[campo["nombre"]].text == "Sí" if campo["tipo"] == "bool" else
                 (float(self.respuestas[campo["nombre"]].text) if campo["tipo"] == "float" else self.respuestas[campo["nombre"]].text))
            )
            for campo in self.campos
        }
        print("Datos a enviar reporte diario:", datos)  # Log datos before sending

        try:
            response = requests.post(f"{API_URL}/recibir-datos", json=datos, timeout=20)
            response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
            if response.status_code == 200:
                print("Datos enviados exitosamente")
                self.materiales_seleccionados = []
                self.equipos_seleccionados = []
                self.vehiculos_seleccionados = []
                self.personal_seleccionados = []
                self.new_materials_to_add = []  # Clear new materials list
                self.new_equipos_to_add = []  # Clear new equipos list
                self.new_vehiculos_to_add = []  # Clear new vehiculos list
                self.new_personal_to_add = []  # Clear new personal list
                self.show_success_popup()
                self.submit_button.disabled = True  # Disable button after successful submission # Removed, submit_button not defined here.
                self.main_app.reporte_diario_submitted = True  # Set flag in main app
                instance.disabled = True  # Disable the button that called this function.
            else:
                print(f"Error al enviar datos: {response.status_code} - {response.text}")
        except requests.exceptions.ConnectionError:
            print(f"Error: No se pudo conectar al servidor para enviar los datos.")
        except requests.exceptions.Timeout:
            print(f"Error: Tiempo de espera agotado al enviar los datos al servidor.")
        except Exception as e:
            print(f"Error al enviar los datos: {e}")
            if isinstance(e, requests.exceptions.RequestException):  # Check if it's a RequestException
                print(f"Detailed error: {e.response.text if e.response else e}")  # Print response text if available
            else:  # Handle other exceptions
                print(f"Detailed error: {e}")

    def validar_datos(self):
        for campo in self.campos:
            if campo["nombre"] == "materiales_usados":
                if not self.materiales_seleccionados:
                    print("Debe seleccionar al menos un material.")
                    return False
            elif campo["nombre"] == "equipos_usados":
                if not self.equipos_seleccionados:
                    print("Debe seleccionar al menos un equipo.")
                    return False
            elif campo["nombre"] == "vehiculos_usados":
                if not self.vehiculos_seleccionados:
                    print("Debe seleccionar al menos un vehículo.")
                    return False
            elif campo["nombre"] == "personal_de_campo":
                if not self.personal_seleccionados:
                    print("Debe seleccionar al menos un personal de campo.")
                    return False
            else:
                valor = self.respuestas[campo["nombre"]].text
                if campo["nombre"] == "fecha":
                    try:
                        datetime.strptime(valor, '%d/%m/%Y')
                    except ValueError:
                        print("Formato de fecha inválido. Use DD/MM/YYYY.")
                        return False
                if not valor and campo["nombre"] not in ["observaciones", "incidentes", "siguiente_dia"]:
                    print(f"El campo '{campo['etiqueta']}' es obligatorio.")
                    return False
                if campo["tipo"] == "float":
                    try:
                        float(valor)
                    except ValueError:
                        print(f"El campo '{campo['etiqueta']}' debe ser un número.")
                        return False
        return True


if __name__ == '__main__':
    ReporteObraApp().run()